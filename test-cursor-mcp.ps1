# Cursor MCP MySQL 连接测试脚本
# 作者: AI Assistant
# 描述: 测试Cursor IDE中的MCP MySQL连接

param(
  [switch]$Help
)

if ($Help) {
  Write-Host "Cursor MCP MySQL 连接测试脚本" -ForegroundColor Green
  Write-Host ""
  Write-Host "功能:"
  Write-Host "  - 验证MCP配置文件"
  Write-Host "  - 测试MySQL连接"
  Write-Host "  - 模拟Cursor MCP调用"
  Write-Host "  - 提供故障排除建议"
  exit 0
}

Write-Host "============================================" -ForegroundColor Green
Write-Host "     Cursor MCP MySQL 连接测试" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green
Write-Host ""

# 1. 检查MCP配置文件
Write-Host "1. 检查Cursor MCP配置..." -ForegroundColor Yellow
$mcpConfigPath = ".cursor/mcp.json"

if (Test-Path $mcpConfigPath) {
  Write-Host "   ✓ MCP配置文件存在: $mcpConfigPath" -ForegroundColor Green
    
  try {
    $mcpConfig = Get-Content $mcpConfigPath | ConvertFrom-Json
    $mysqlConfig = $mcpConfig.mcpServers.MySQL
        
    Write-Host "   配置详情:" -ForegroundColor Cyan
    Write-Host "     命令: $($mysqlConfig.command)" -ForegroundColor White
    Write-Host "     参数: $($mysqlConfig.args -join ' ')" -ForegroundColor White
    Write-Host "     MySQL主机: $($mysqlConfig.env.MYSQL_HOST)" -ForegroundColor White
    Write-Host "     MySQL端口: $($mysqlConfig.env.MYSQL_PORT)" -ForegroundColor White
    Write-Host "     MySQL用户: $($mysqlConfig.env.MYSQL_USER)" -ForegroundColor White
    Write-Host "     MySQL数据库: $($mysqlConfig.env.MYSQL_DB)" -ForegroundColor White
    Write-Host "     允许插入: $($mysqlConfig.env.ALLOW_INSERT_OPERATION)" -ForegroundColor White
    Write-Host "     允许更新: $($mysqlConfig.env.ALLOW_UPDATE_OPERATION)" -ForegroundColor White
    Write-Host "     允许删除: $($mysqlConfig.env.ALLOW_DELETE_OPERATION)" -ForegroundColor White
        
  }
  catch {
    Write-Host "   ✗ 配置文件格式错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
  }
}
else {
  Write-Host "   ✗ MCP配置文件不存在: $mcpConfigPath" -ForegroundColor Red
  Write-Host "   请确保在项目根目录下有正确的MCP配置文件" -ForegroundColor Yellow
  exit 1
}

# 2. 测试MySQL连接
Write-Host ""
Write-Host "2. 测试MySQL连接..." -ForegroundColor Yellow
try {
  $mysqlHost = $mysqlConfig.env.MYSQL_HOST
  $mysqlPort = $mysqlConfig.env.MYSQL_PORT
    
  Write-Host "   正在测试连接到 ${mysqlHost}:${mysqlPort}..." -ForegroundColor Cyan
  $connection = Test-NetConnection -ComputerName $mysqlHost -Port $mysqlPort -WarningAction SilentlyContinue
    
  if ($connection.TcpTestSucceeded) {
    Write-Host "   ✓ MySQL网络连接成功" -ForegroundColor Green
  }
  else {
    Write-Host "   ✗ MySQL网络连接失败" -ForegroundColor Red
    Write-Host "   请检查MySQL服务是否运行，防火墙设置等" -ForegroundColor Yellow
  }
}
catch {
  Write-Host "   ⚠ 网络测试失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 3. 模拟MCP调用
Write-Host ""
Write-Host "3. 模拟MCP服务器调用..." -ForegroundColor Yellow

# 设置环境变量
$mysqlConfig.env.PSObject.Properties | ForEach-Object {
  [Environment]::SetEnvironmentVariable($_.Name, $_.Value, "Process")
}

try {
  Write-Host "   正在测试MCP服务器启动..." -ForegroundColor Cyan
    
  # 使用timeout命令限制执行时间
  $output = cmd /c "timeout 10 npx @benborla29/mcp-server-mysql 2>&1"
    
  if ($output -match "error|Error|ERROR") {
    Write-Host "   ✗ MCP服务器启动失败" -ForegroundColor Red
    Write-Host "   错误输出: $output" -ForegroundColor Yellow
  }
  elseif ($output -match "listening|started|ready") {
    Write-Host "   ✓ MCP服务器启动成功" -ForegroundColor Green
  }
  else {
    Write-Host "   ⚠ MCP服务器状态不明确" -ForegroundColor Yellow
    Write-Host "   输出: $output" -ForegroundColor White
  }
}
catch {
  Write-Host "   ✗ 无法启动MCP服务器: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 故障排除建议
Write-Host ""
Write-Host "4. 故障排除建议..." -ForegroundColor Yellow

$suggestions = @()

if (-not $connection.TcpTestSucceeded) {
  $suggestions += "MySQL连接失败 - 请检查MySQL服务是否运行，端口是否正确"
}

if ($mysqlConfig.env.MYSQL_HOST -ne "127.0.0.1" -and $mysqlConfig.env.MYSQL_HOST -ne "localhost") {
  $suggestions += "使用远程MySQL - 请确保远程连接权限和防火墙设置正确"
}

if (-not $mysqlConfig.env.MYSQL_ENABLE_LOGGING) {
  $suggestions += "建议启用MySQL日志记录以便调试"
}

if ($suggestions.Count -gt 0) {
  Write-Host "   发现潜在问题:" -ForegroundColor Yellow
  $suggestions | ForEach-Object { Write-Host "     • $_" -ForegroundColor White }
}
else {
  Write-Host "   ✓ 配置看起来正常" -ForegroundColor Green
}

Write-Host ""
Write-Host "============================================" -ForegroundColor Green
Write-Host "如果MCP仍然无法在Cursor中工作，请尝试:" -ForegroundColor Green
Write-Host "  1. 重启Cursor IDE" -ForegroundColor White
Write-Host "  2. 检查Cursor的MCP扩展是否已启用" -ForegroundColor White
Write-Host "  3. 查看Cursor的开发者控制台错误日志" -ForegroundColor White
Write-Host "  4. 确保MySQL数据库和表已创建" -ForegroundColor White
Write-Host "============================================" -ForegroundColor Green 