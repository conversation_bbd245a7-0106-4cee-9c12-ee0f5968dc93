// 测试MySQL连接
const mysql = require('mysql2/promise');

async function testConnection() {
  console.log('测试MySQL连接...');
  
  const config = {
    host: '*************',
    port: 3306,
    user: 'onlyfile_user',
    password: '0nlyF!le$ecure#123',
    database: 'onlyfile'
  };
  
  console.log('连接配置:', {
    host: config.host,
    port: config.port,
    user: config.user,
    database: config.database
  });
  
  try {
    console.log('正在连接...');
    const connection = await mysql.createConnection(config);
    console.log('✓ 连接成功！');
    
    // 测试查询
    console.log('执行测试查询...');
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✓ 查询成功:', rows);
    
    // 获取数据库信息
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('✓ 数据库表:', tables);
    
    await connection.end();
    console.log('✓ 连接已关闭');
    
  } catch (error) {
    console.log('✗ 连接失败:', error.message);
    console.log('错误代码:', error.code);
    console.log('错误详情:', error);
  }
}

testConnection();
