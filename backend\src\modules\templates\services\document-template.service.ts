import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { DatabaseService } from '../../database/services/database.service';
import { FilenetService } from '../../filenet/services/filenet.service';
import { v4 as uuidv4 } from 'uuid';
import { TemplateRow } from '../../database/types/database.types';

/**
 * 文档模板查询结果接口
 */
export interface DocumentTemplateRow extends TemplateRow {
  category_name?: string;
  creator_name?: string;
  updater_name?: string;
  source_document_name?: string;
  source_document_extension?: string;
  file_size?: number;
  mime_type?: string;
  extension?: string;
}

/**
 * 统计查询结果接口
 */
interface CountQueryResult {
  total: number;
}

export interface CreateTemplateData {
  name: string;
  categoryId?: string;
  description?: string;
  createdBy?: string;
}

export interface UpdateTemplateData {
  name?: string;
  category_id?: string;
  description?: string;
  status?: string;
  updated_by?: string;
}

export interface TemplateFile {
  originalname: string;
  size: number;
  filename?: string;
  path?: string;
  mimetype?: string;
  destination?: string;
  [key: string]: unknown;
}

/**
 * 文档模板服务
 * 专门管理FileNet上传的合同模板等文档模板
 * 区别于OnlyOffice配置模板，此服务管理实际的文档文件模板
 */
@Injectable()
export class DocumentTemplateService {
  constructor(
    private databaseService: DatabaseService,
    private filenetService: FilenetService,
  ) {}

  /**
   * 获取文档模板列表
   */
  async getDocumentTemplates(options: {
    limit?: number;
    offset?: number;
    categoryId?: string;
    status?: string;
    sortBy?: string;
    order?: string;
  } = {}) {
    try {
      const { limit = 10, offset = 0, categoryId, status = 'enabled', sortBy = 'created_at', order = 'DESC' } = options;

      // 构建查询条件
      const conditions = ['t.is_deleted = FALSE'];
      const queryParams = [];

      if (status && status.toLowerCase() !== 'all') {
        conditions.push('t.status = ?');
        queryParams.push(status);
      }

      if (categoryId) {
        conditions.push('t.category_id = ?');
        queryParams.push(categoryId);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // 主查询
      const validSortColumns = ['name', 'created_at', 'updated_at'];
      const sortColumn = validSortColumns.includes(sortBy) ? `t.${sortBy}` : 't.created_at';
      const sortOrder = (order.toUpperCase() === 'ASC' || order.toUpperCase() === 'DESC') ? order.toUpperCase() : 'DESC';

      const mainQuery = `
        SELECT 
          t.*, 
          tc.name as category_name, 
          fd.original_name as source_document_name,
          fd.file_size,
          fd.extension
        FROM templates t 
        LEFT JOIN template_categories tc ON t.category_id = tc.id 
        LEFT JOIN filenet_documents fd ON t.doc_id = fd.id 
        ${whereClause} 
        ORDER BY ${sortColumn} ${sortOrder} 
        LIMIT ? OFFSET ?
      `;
      const mainParams = [...queryParams, limit, offset];

      // 计数查询
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM templates t
        LEFT JOIN template_categories tc ON t.category_id = tc.id
        ${whereClause}
      `;

      const [templates, countResult] = await Promise.all([
        this.databaseService.query(mainQuery, mainParams) as unknown as DocumentTemplateRow[],
        this.databaseService.query(countQuery, queryParams) as unknown as CountQueryResult[]
      ]);

      const total = countResult[0]?.total || 0;

      return {
        success: true,
        data: {
          templates,
          total,
          limit,
          offset
        }
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 获取文档模板列表失败:', error);
      throw new HttpException(
        `获取文档模板列表失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 根据ID获取文档模板详情
   */
  async getDocumentTemplateById(templateId: string) {
    try {
      const query = `
        SELECT 
          t.*, 
          tc.name as category_name, 
          fd.original_name as source_document_name, 
          fd.extension as source_document_extension,
          fd.file_size,
          fd.mime_type
        FROM templates t 
        LEFT JOIN template_categories tc ON t.category_id = tc.id 
        LEFT JOIN filenet_documents fd ON t.doc_id = fd.id 
        WHERE t.id = ? AND t.is_deleted = FALSE
      `;

      const result = await this.databaseService.queryOne(query, [templateId]);
      
      if (!result) {
        throw new HttpException('文档模板不存在', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 获取文档模板详情失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `获取文档模板详情失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 创建文档模板
   */
  async createDocumentTemplate(templateData: CreateTemplateData, file?: TemplateFile) {
    try {
      const { name, categoryId, description, createdBy = 'system' } = templateData;

      if (!name) {
        throw new HttpException('模板名称不能为空', HttpStatus.BAD_REQUEST);
      }

      let docId: string;

      if (file) {
        console.log('[DocumentTemplateService] 上传模板文件到FileNet:', file.originalname);
        
        // 上传文件到FileNet
        const uploadResult = await this.filenetService.uploadDocument(file, file.originalname, createdBy);
        
        if (!uploadResult.success) {
          throw new HttpException('模板文件上传失败', HttpStatus.INTERNAL_SERVER_ERROR);
        }

        docId = uploadResult.dbId; // 使用数据库ID
      } else {
        throw new HttpException('必须提供模板文件', HttpStatus.BAD_REQUEST);
      }

      // 创建模板记录
      const templateId = uuidv4();
      await this.databaseService.query(
        'INSERT INTO templates (id, name, doc_id, category_id, description, created_by, status) VALUES (?, ?, ?, ?, ?, ?, ?)',
        [templateId, name, docId, categoryId || null, description || '', createdBy, 'enabled']
      );

      return await this.getDocumentTemplateById(templateId);
    } catch (error) {
      console.error('[DocumentTemplateService] 创建文档模板失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `创建文档模板失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新文档模板
   */
  async updateDocumentTemplate(templateId: string, updateData: UpdateTemplateData) {
    try {
      const allowedUpdates = ['name', 'category_id', 'description', 'status', 'updated_by'];
      const updates: Record<string, unknown> = {};
      const querySet: string[] = [];

      for (const key in updateData) {
        if (allowedUpdates.includes(key) && updateData[key] !== undefined) {
          updates[key] = updateData[key];
          querySet.push(`${key} = ?`);
        }
      }

      if (querySet.length === 0) {
        throw new HttpException('没有提供有效的更新字段', HttpStatus.BAD_REQUEST);
      }

      const query = `UPDATE templates SET ${querySet.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND is_deleted = FALSE`;
      const params = [...Object.values(updates), templateId];

      const result = await this.databaseService.query(query, params) as unknown as { affectedRows: number };

      if (!result || result.affectedRows === 0) {
        throw new HttpException('文档模板不存在或未更新', HttpStatus.NOT_FOUND);
      }

      return await this.getDocumentTemplateById(templateId);
    } catch (error) {
      console.error('[DocumentTemplateService] 更新文档模板失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `更新文档模板失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 删除文档模板 (软删除)
   */
  async deleteDocumentTemplate(templateId: string) {
    try {
      const query = 'UPDATE templates SET is_deleted = TRUE, status = \'disabled\', updated_at = CURRENT_TIMESTAMP WHERE id = ? AND is_deleted = FALSE';
      
      const result = await this.databaseService.query(query, [templateId]) as unknown as { affectedRows: number };

      if (!result || result.affectedRows === 0) {
        throw new HttpException('文档模板不存在', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        message: '文档模板删除成功'
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 删除文档模板失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `删除文档模板失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 基于模板创建新文档
   */
  async createDocumentFromTemplate(templateId: string, newDocumentName: string, _userId: string = 'system') {
    try {
      if (!templateId || !newDocumentName) {
        throw new HttpException('模板ID和新文档名称不能为空', HttpStatus.BAD_REQUEST);
      }

      // 获取模板信息
      const template = await this.getDocumentTemplateById(templateId);
      if (!template.success || !template.data || (template.data as DocumentTemplateRow).status !== 'enabled') {
        throw new HttpException('模板不存在、已被删除或已禁用', HttpStatus.NOT_FOUND);
      }

      const templateData = template.data as DocumentTemplateRow;

      // 获取源文档信息
      const sourceDocument = await this.databaseService.queryOne(
        'SELECT * FROM filenet_documents WHERE id = ? AND is_deleted = FALSE',
        [templateData.doc_id]
      ) as unknown as DocumentTemplateRow;

      if (!sourceDocument) {
        throw new HttpException('模板的源文档不存在或无法访问', HttpStatus.NOT_FOUND);
      }

      // 这里可以扩展实现复制FileNet文档的逻辑
      // 当前简化实现，返回创建成功的状态

      return {
        success: true,
        message: '基于模板创建文档成功',
        data: {
          templateId,
          templateName: templateData.name,
          newDocumentName,
          sourceDocumentId: templateData.doc_id
        }
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 基于模板创建文档失败:', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `基于模板创建文档失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取模板分类
   */
  async getTemplateCategories() {
    try {
      const categories = await this.databaseService.query(
        'SELECT * FROM template_categories ORDER BY sort_order ASC, name ASC'
      );

      return {
        success: true,
        data: categories
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 获取模板分类失败:', error);
      throw new HttpException(
        `获取模板分类失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 创建模板分类
   */
  async createTemplateCategory(categoryData: {
    name: string;
    parentId?: string;
    description?: string;
    sortOrder?: number;
  }) {
    try {
      const { name, parentId, description, sortOrder = 0 } = categoryData;

      if (!name) {
        throw new HttpException('分类名称不能为空', HttpStatus.BAD_REQUEST);
      }

      const categoryId = uuidv4();
      await this.databaseService.query(
        'INSERT INTO template_categories (id, name, parent_id, description, sort_order) VALUES (?, ?, ?, ?, ?)',
        [categoryId, name, parentId || null, description || '', sortOrder]
      );

      const newCategory = await this.databaseService.queryOne(
        'SELECT * FROM template_categories WHERE id = ?',
        [categoryId]
      );

      return {
        success: true,
        data: newCategory
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 创建模板分类失败:', error);
      throw new HttpException(
        `创建模板分类失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取配置模板列表 (用于统一模板概览)
   */
  private async getConfigTemplates() {
    try {
      const configTemplates = await this.databaseService.query(`
        SELECT id, name, description, is_default, is_active, created_at, updated_at
        FROM config_templates 
        WHERE is_active = TRUE 
        ORDER BY is_default DESC, created_at ASC
      `);

      return configTemplates;
    } catch (error) {
      console.error('[DocumentTemplateService] 获取配置模板失败:', error);
      return [];
    }
  }

  /**
   * 获取所有模板概览 (包括文档模板和配置模板)
   */
  async getAllTemplatesOverview() {
    try {
      const [documentTemplates, configTemplates] = await Promise.all([
        this.getDocumentTemplates({ limit: 100 }),
        this.getConfigTemplates()
      ]);

      return {
        success: true,
        data: {
          documentTemplates: documentTemplates.data,
          configTemplates,
          summary: {
            totalDocumentTemplates: documentTemplates.data.total,
            totalConfigTemplates: configTemplates.length,
            totalTemplates: documentTemplates.data.total + configTemplates.length
          }
        }
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 获取模板概览失败:', error);
      throw new HttpException(
        `获取模板概览失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 搜索模板 (文档模板和配置模板)
   */
  async searchTemplates(searchOptions: {
    keyword?: string;
    type?: string;
    limit?: number;
  }) {
    try {
      const { keyword, type, limit = 20 } = searchOptions;

      const results: {
        documentTemplates: unknown[];
        configTemplates: unknown[];
        total: number;
      } = {
        documentTemplates: [],
        configTemplates: [],
        total: 0
      };

      // 搜索文档模板
      if (!type || type === 'document') {
        const documentResult = await this.getDocumentTemplates({
          limit,
          status: 'all'
        });

        results.documentTemplates = documentResult.data.templates.filter((template: { name: string; description?: string }) =>
          !keyword ||
          template.name.toLowerCase().includes(keyword.toLowerCase()) ||
          (template.description && template.description.toLowerCase().includes(keyword.toLowerCase()))
        );
      }

      // 搜索配置模板
      if (!type || type === 'config') {
        const configResult = await this.getConfigTemplates();
        
        results.configTemplates = configResult.filter((template: { name: string; description?: string }) =>
          !keyword ||
          template.name.toLowerCase().includes(keyword.toLowerCase()) ||
          (template.description && template.description.toLowerCase().includes(keyword.toLowerCase()))
        );
      }

      results.total = results.documentTemplates.length + results.configTemplates.length;

      return {
        success: true,
        data: results
      };
    } catch (error) {
      console.error('[DocumentTemplateService] 搜索模板失败:', error);
      throw new HttpException(
        `搜索模板失败: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
} 