-- 清理重复文档数据脚本
-- 将错误创建在主表中的重复文档移动到版本表中

-- 开始事务
START TRANSACTION;

-- 1. 首先备份当前数据（可选，建议在生产环境执行）
-- CREATE TABLE filenet_documents_backup AS SELECT * FROM filenet_documents;
-- CREATE TABLE filenet_document_versions_backup AS SELECT * FROM filenet_document_versions;

-- 2. 分析重复的poems_answer.docx文档
-- 保留最早创建的一个作为主文档，其他的移动到版本表

-- 查看poems_answer.docx的重复情况
SELECT 
    id,
    original_name,
    created_at,
    version,
    fn_doc_id,
    file_size,
    file_hash
FROM filenet_documents 
WHERE original_name = 'poems_answer.docx' 
ORDER BY created_at ASC;

-- 3. 处理poems_answer.docx重复文档
-- 找到最早的那个作为主文档，其他的转为版本
SET @main_poems_doc_id = (
    SELECT id 
    FROM filenet_documents 
    WHERE original_name = 'poems_answer.docx' 
    ORDER BY created_at ASC 
    LIMIT 1
);

-- 将其他重复的poems_answer.docx文档移动到版本表
INSERT INTO filenet_document_versions (
    id,
    doc_id,
    fn_doc_id,
    version,
    file_hash,
    modified_by,
    modified_at,
    file_size,
    comment
)
SELECT 
    CONCAT('cleanup-', UUID()) as id,
    @main_poems_doc_id as doc_id,
    fn_doc_id,
    ROW_NUMBER() OVER (ORDER BY created_at) + 
        (SELECT COALESCE(MAX(version), 0) FROM filenet_document_versions WHERE doc_id = @main_poems_doc_id) as version,
    file_hash,
    created_by as modified_by,
    created_at as modified_at,
    file_size,
    CONCAT('从重复主文档迁移 (原ID: ', id, ')') as comment
FROM filenet_documents 
WHERE original_name = 'poems_answer.docx' 
    AND id != @main_poems_doc_id
    AND created_at > (SELECT created_at FROM filenet_documents WHERE id = @main_poems_doc_id);

-- 更新主文档的版本号为最新版本
UPDATE filenet_documents 
SET version = (
    SELECT MAX(version) 
    FROM filenet_document_versions 
    WHERE doc_id = @main_poems_doc_id
)
WHERE id = @main_poems_doc_id;

-- 删除重复的主文档记录
DELETE FROM filenet_documents 
WHERE original_name = 'poems_answer.docx' 
    AND id != @main_poems_doc_id;

-- 4. 处理张艺晗数学错题集重复文档
-- 找到最早的那个作为主文档
SET @main_math_doc_id = (
    SELECT id 
    FROM filenet_documents 
    WHERE original_name LIKE '张艺晗数学错题集%' 
    ORDER BY created_at ASC 
    LIMIT 1
);

-- 将其他重复的数学错题集文档移动到版本表
INSERT INTO filenet_document_versions (
    id,
    doc_id,
    fn_doc_id,
    version,
    file_hash,
    modified_by,
    modified_at,
    file_size,
    comment
)
SELECT 
    CONCAT('cleanup-math-', UUID()) as id,
    @main_math_doc_id as doc_id,
    fn_doc_id,
    ROW_NUMBER() OVER (ORDER BY created_at) + 
        (SELECT COALESCE(MAX(version), 0) FROM filenet_document_versions WHERE doc_id = @main_math_doc_id) as version,
    file_hash,
    created_by as modified_by,
    created_at as modified_at,
    file_size,
    CONCAT('从重复主文档迁移 (原ID: ', id, ')') as comment
FROM filenet_documents 
WHERE original_name LIKE '张艺晗数学错题集%' 
    AND id != @main_math_doc_id
    AND created_at > (SELECT created_at FROM filenet_documents WHERE id = @main_math_doc_id);

-- 更新主文档的版本号
UPDATE filenet_documents 
SET version = (
    SELECT MAX(version) 
    FROM filenet_document_versions 
    WHERE doc_id = @main_math_doc_id
)
WHERE id = @main_math_doc_id;

-- 删除重复的主文档记录
DELETE FROM filenet_documents 
WHERE original_name LIKE '张艺晗数学错题集%' 
    AND id != @main_math_doc_id;

-- 5. 处理其他可能的重复文档
-- 查找所有按文件名分组有多个记录的文档
SELECT 
    original_name,
    COUNT(*) as count,
    GROUP_CONCAT(id) as ids,
    GROUP_CONCAT(created_at) as created_dates
FROM filenet_documents 
GROUP BY original_name 
HAVING COUNT(*) > 1;

-- 6. 验证清理结果
SELECT 
    '清理后主文档数量' as description,
    COUNT(*) as count 
FROM filenet_documents;

SELECT 
    '版本记录数量' as description,
    COUNT(*) as count 
FROM filenet_document_versions;

SELECT 
    '仍有重复的文档' as description,
    original_name,
    COUNT(*) as count
FROM filenet_documents 
GROUP BY original_name 
HAVING COUNT(*) > 1;

-- 提交事务（谨慎使用，建议先在测试环境验证）
-- COMMIT;

-- 如果出现问题，可以回滚
-- ROLLBACK; 