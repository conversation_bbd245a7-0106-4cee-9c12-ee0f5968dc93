# 调试MCP MySQL服务器启动问题

Write-Host "调试MCP MySQL服务器启动问题" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:MYSQL_HOST = "*************"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "onlyfile_user"
$env:MYSQL_PASS = "0nlyF!le`$ecure#123"
$env:MYSQL_DB = "onlyfile"
$env:MYSQL_ENABLE_LOGGING = "true"
$env:ALLOW_INSERT_OPERATION = "true"
$env:ALLOW_UPDATE_OPERATION = "true"
$env:ALLOW_DELETE_OPERATION = "false"

Write-Host "环境变量设置:" -ForegroundColor Yellow
Write-Host "MYSQL_HOST: $env:MYSQL_HOST"
Write-Host "MYSQL_PORT: $env:MYSQL_PORT"
Write-Host "MYSQL_USER: $env:MYSQL_USER"
Write-Host "MYSQL_DB: $env:MYSQL_DB"
Write-Host ""

Write-Host "1. 检查MCP包是否正确安装..." -ForegroundColor Yellow
try {
    $packageInfo = npm list -g @benborla29/mcp-server-mysql 2>&1
    Write-Host "包信息: $packageInfo" -ForegroundColor Cyan
}
catch {
    Write-Host "获取包信息失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "2. 尝试直接运行MCP服务器..." -ForegroundColor Yellow

# 创建一个PowerShell作业来运行MCP服务器
$job = Start-Job -ScriptBlock {
    param($host, $port, $user, $pass, $db)
    
    $env:MYSQL_HOST = $host
    $env:MYSQL_PORT = $port
    $env:MYSQL_USER = $user
    $env:MYSQL_PASS = $pass
    $env:MYSQL_DB = $db
    $env:MYSQL_ENABLE_LOGGING = "true"
    
    try {
        # 捕获所有输出
        $output = & npx @benborla29/mcp-server-mysql 2>&1
        return @{
            Success = $true
            Output = $output
            ExitCode = $LASTEXITCODE
        }
    }
    catch {
        return @{
            Success = $false
            Error = $_.Exception.Message
            ExitCode = $LASTEXITCODE
        }
    }
} -ArgumentList $env:MYSQL_HOST, $env:MYSQL_PORT, $env:MYSQL_USER, $env:MYSQL_PASS, $env:MYSQL_DB

# 等待5秒
Write-Host "等待5秒查看输出..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# 检查作业状态
$jobResult = Receive-Job -Job $job
$jobState = $job.State

Write-Host ""
Write-Host "作业状态: $jobState" -ForegroundColor Yellow

if ($jobResult) {
    Write-Host "输出结果:" -ForegroundColor Cyan
    if ($jobResult.Success) {
        Write-Host "成功: $($jobResult.Success)" -ForegroundColor Green
        Write-Host "输出: $($jobResult.Output)" -ForegroundColor White
        Write-Host "退出代码: $($jobResult.ExitCode)" -ForegroundColor White
    } else {
        Write-Host "失败: $($jobResult.Error)" -ForegroundColor Red
        Write-Host "退出代码: $($jobResult.ExitCode)" -ForegroundColor White
    }
} else {
    Write-Host "没有输出结果" -ForegroundColor Yellow
}

# 清理作业
Remove-Job -Job $job -Force

Write-Host ""
Write-Host "3. 尝试使用node直接运行..." -ForegroundColor Yellow

# 查找MCP服务器的实际路径
$globalNodeModules = npm root -g
$mcpPath = Join-Path $globalNodeModules "@benborla29\mcp-server-mysql\dist\index.js"

Write-Host "MCP路径: $mcpPath" -ForegroundColor Cyan

if (Test-Path $mcpPath) {
    Write-Host "文件存在，尝试直接运行..." -ForegroundColor Green
    
    try {
        # 使用Start-Process来捕获输出
        $psi = New-Object System.Diagnostics.ProcessStartInfo
        $psi.FileName = "node"
        $psi.Arguments = "`"$mcpPath`""
        $psi.UseShellExecute = $false
        $psi.RedirectStandardOutput = $true
        $psi.RedirectStandardError = $true
        $psi.CreateNoWindow = $true
        
        # 设置环境变量
        $psi.EnvironmentVariables["MYSQL_HOST"] = $env:MYSQL_HOST
        $psi.EnvironmentVariables["MYSQL_PORT"] = $env:MYSQL_PORT
        $psi.EnvironmentVariables["MYSQL_USER"] = $env:MYSQL_USER
        $psi.EnvironmentVariables["MYSQL_PASS"] = $env:MYSQL_PASS
        $psi.EnvironmentVariables["MYSQL_DB"] = $env:MYSQL_DB
        $psi.EnvironmentVariables["MYSQL_ENABLE_LOGGING"] = "true"
        
        $process = [System.Diagnostics.Process]::Start($psi)
        
        # 等待3秒
        $timeout = 3000
        if ($process.WaitForExit($timeout)) {
            $stdout = $process.StandardOutput.ReadToEnd()
            $stderr = $process.StandardError.ReadToEnd()
            
            Write-Host "进程已退出" -ForegroundColor Yellow
            Write-Host "退出代码: $($process.ExitCode)" -ForegroundColor White
            
            if ($stdout) {
                Write-Host "标准输出:" -ForegroundColor Green
                Write-Host $stdout -ForegroundColor White
            }
            
            if ($stderr) {
                Write-Host "错误输出:" -ForegroundColor Red
                Write-Host $stderr -ForegroundColor White
            }
        } else {
            Write-Host "进程仍在运行，强制停止..." -ForegroundColor Yellow
            $process.Kill()
            Write-Host "这表明MCP服务器可能正常启动了" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "运行失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "MCP文件不存在: $mcpPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "调试完成！" -ForegroundColor Green
