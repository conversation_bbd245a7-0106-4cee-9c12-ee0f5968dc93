# MCP MySQL 测试脚本
# 作者: AI Assistant
# 描述: 测试MCP MySQL服务器连接和配置

param(
  [switch]$Help
)

if ($Help) {
  Write-Host "MCP MySQL 测试脚本" -ForegroundColor Green
  Write-Host ""
  Write-Host "用法: .\test-mcp-mysql.ps1 [-Help]" -ForegroundColor Yellow
  Write-Host ""
  Write-Host "功能:"
  Write-Host "  - 检查Node.js环境"
  Write-Host "  - 测试MySQL连接"
  Write-Host "  - 验证MCP包可用性"
  Write-Host "  - 启动MCP服务器"
  exit 0
}

Write-Host "============================================" -ForegroundColor Green
Write-Host "       MCP MySQL 环境测试脚本" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green
Write-Host ""

# 1. 检查Node.js环境
Write-Host "1. 检查Node.js环境..." -ForegroundColor Yellow
try {
  $nodeVersion = node --version
  Write-Host "   ✓ Node.js版本: $nodeVersion" -ForegroundColor Green
    
  $npmVersion = npm --version
  Write-Host "   ✓ NPM版本: $npmVersion" -ForegroundColor Green
}
catch {
  Write-Host "   ✗ Node.js或NPM未找到" -ForegroundColor Red
  Write-Host "   请确保Node.js已正确安装" -ForegroundColor Yellow
  exit 1
}

# 2. 设置配置
Write-Host ""
Write-Host "2. 设置MySQL配置..." -ForegroundColor Yellow

# 硬编码配置（您的配置）
$config = @{
  'MYSQL_HOST' = '*************'
  'MYSQL_PORT' = '3306'
  'MYSQL_USER' = 'onlyfile_user'
  'MYSQL_PASS' = '0nlyF!le$ecure#123'
  'MYSQL_DB' = 'onlyfile'
  'ALLOW_INSERT_OPERATION' = 'true'
  'ALLOW_UPDATE_OPERATION' = 'true'
  'ALLOW_DELETE_OPERATION' = 'false'
  'MYSQL_ENABLE_LOGGING' = 'true'
}

Write-Host "   配置内容:" -ForegroundColor Cyan
Write-Host "     MySQL主机: $($config['MYSQL_HOST'])" -ForegroundColor White
Write-Host "     MySQL端口: $($config['MYSQL_PORT'])" -ForegroundColor White
Write-Host "     MySQL用户: $($config['MYSQL_USER'])" -ForegroundColor White
Write-Host "     MySQL数据库: $($config['MYSQL_DB'])" -ForegroundColor White

# 3. 检查MCP包可用性
Write-Host ""
Write-Host "3. 检查MCP MySQL包..." -ForegroundColor Yellow
try {
  Write-Host "   正在下载MCP MySQL包信息..." -ForegroundColor Cyan
  $output = npx @benborla29/mcp-server-mysql --help 2>&1
  if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✓ MCP MySQL包可用" -ForegroundColor Green
  }
  else {
    Write-Host "   ✗ MCP MySQL包不可用" -ForegroundColor Red
    Write-Host "   输出: $output" -ForegroundColor Yellow
  }
}
catch {
  Write-Host "   ✗ 无法检查MCP MySQL包" -ForegroundColor Red
  Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 4. 网络连接测试
Write-Host ""
Write-Host "4. 测试网络连接..." -ForegroundColor Yellow
try {
  $mysqlHost = $config['MYSQL_HOST']
  $mysqlPort = $config['MYSQL_PORT']
    
  if ($mysqlHost -and $mysqlPort) {
    Write-Host "   正在测试连接到 ${mysqlHost}:${mysqlPort}..." -ForegroundColor Cyan
    $connection = Test-NetConnection -ComputerName $mysqlHost -Port $mysqlPort -WarningAction SilentlyContinue
        
    if ($connection.TcpTestSucceeded) {
      Write-Host "   ✓ 网络连接成功" -ForegroundColor Green
    }
    else {
      Write-Host "   ✗ 网络连接失败" -ForegroundColor Red
      Write-Host "   请检查MySQL服务是否运行，防火墙设置等" -ForegroundColor Yellow
    }
  }
  else {
    Write-Host "   ⚠ 跳过网络测试 (缺少主机或端口配置)" -ForegroundColor Yellow
  }
}
catch {
  Write-Host "   ⚠ 网络测试失败: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "============================================" -ForegroundColor Green
Write-Host "测试完成！" -ForegroundColor Green

Write-Host ""
Write-Host "提醒: 配置中使用的是您指定的IP地址 *************" -ForegroundColor Yellow
Write-Host "请确保:" -ForegroundColor Yellow
Write-Host "  1. MySQL服务在该IP地址上运行" -ForegroundColor White
Write-Host "  2. 防火墙允许3306端口访问" -ForegroundColor White
Write-Host "  3. MySQL用户有远程连接权限" -ForegroundColor White

Write-Host ""
Write-Host "5. 启动MCP服务器测试..." -ForegroundColor Yellow

# 设置环境变量
foreach ($key in $config.Keys) {
  Set-Item -Path "env:$key" -Value $config[$key]
}

Write-Host "   正在启动MCP MySQL服务器..." -ForegroundColor Cyan
Write-Host "   按 Ctrl+C 停止服务器" -ForegroundColor Yellow
Write-Host ""

try {
  npx @benborla29/mcp-server-mysql
}
catch {
  Write-Host "   ✗ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
}