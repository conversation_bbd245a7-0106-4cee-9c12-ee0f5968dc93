// 测试MCP服务器的标准输入/输出通信
const { spawn } = require('child_process');

console.log('测试MCP MySQL服务器的stdio通信...');

// 设置环境变量
const env = {
  ...process.env,
  MYSQL_HOST: '*************',
  MYSQL_PORT: '3306',
  MYSQL_USER: 'onlyfile_user',
  MYSQL_PASS: '0nlyF!le$ecure#123',
  MYSQL_DB: 'onlyfile',
  MYSQL_ENABLE_LOGGING: 'true',
  ALLOW_INSERT_OPERATION: 'true',
  ALLOW_UPDATE_OPERATION: 'true',
  ALLOW_DELETE_OPERATION: 'false'
};

console.log('环境变量设置完成');

// 启动MCP服务器
const mcpProcess = spawn('node', ['C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@benborla29\\mcp-server-mysql\\dist\\index.js'], {
  env: env,
  stdio: ['pipe', 'pipe', 'pipe']
});

console.log('MCP进程已启动，PID:', mcpProcess.pid);

let hasOutput = false;
let hasError = false;

// 监听标准输出
mcpProcess.stdout.on('data', (data) => {
  hasOutput = true;
  console.log('STDOUT:', data.toString());
});

// 监听错误输出
mcpProcess.stderr.on('data', (data) => {
  hasError = true;
  console.log('STDERR:', data.toString());
});

// 监听进程退出
mcpProcess.on('exit', (code, signal) => {
  console.log(`进程退出: 代码=${code}, 信号=${signal}`);
});

mcpProcess.on('error', (err) => {
  console.log('进程错误:', err.message);
});

// 发送MCP初始化消息
setTimeout(() => {
  console.log('发送MCP初始化消息...');
  
  const initMessage = {
    jsonrpc: "2.0",
    id: 1,
    method: "initialize",
    params: {
      protocolVersion: "2024-11-05",
      capabilities: {
        roots: {
          listChanged: true
        },
        sampling: {}
      },
      clientInfo: {
        name: "test-client",
        version: "1.0.0"
      }
    }
  };
  
  try {
    mcpProcess.stdin.write(JSON.stringify(initMessage) + '\n');
    console.log('初始化消息已发送');
  } catch (err) {
    console.log('发送消息失败:', err.message);
  }
}, 1000);

// 10秒后停止测试
setTimeout(() => {
  console.log('测试超时，停止进程...');
  mcpProcess.kill();
  
  console.log('\n=== 测试结果 ===');
  console.log('有输出:', hasOutput);
  console.log('有错误:', hasError);
  
  if (!hasOutput && !hasError) {
    console.log('可能的问题:');
    console.log('1. MCP服务器启动后立即退出');
    console.log('2. 数据库连接失败');
    console.log('3. 环境变量配置错误');
    console.log('4. MCP协议版本不匹配');
  }
  
  process.exit(0);
}, 10000);
