# MCP MySQL Server Configuration
# 基本连接配置 (与.cursor/mcp.json保持一致)
MYSQL_HOST=*************
MYSQL_PORT=3306
MYSQL_USER=onlyfile_user
MYSQL_PASS=0nlyF!le$ecure#123
MYSQL_DB=onlyfile

# 性能配置
MYSQL_POOL_SIZE=10
MYSQL_QUERY_TIMEOUT=30000
MYSQL_CACHE_TTL=60000

# 安全配置 (与.cursor/mcp.json保持一致)
MYSQL_RATE_LIMIT=100
MYSQL_SSL=false
ALLOW_INSERT_OPERATION=true
ALLOW_UPDATE_OPERATION=true
ALLOW_DELETE_OPERATION=false

# 监控配置
MYSQL_ENABLE_LOGGING=true
MYSQL_LOG_LEVEL=info

# 远程MCP配置 (禁用，因为Cursor使用本地模式)
IS_REMOTE_MCP=false
# REMOTE_SECRET_KEY=your-secret-key-here
# PORT=3000 