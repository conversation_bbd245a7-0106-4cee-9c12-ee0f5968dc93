# MCP MySQL 远程服务启动脚本
$env:MYSQL_HOST = "*************"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "onlyfile_user"
$env:MYSQL_PASS = "0nlyF!le$ecure#123"
$env:MYSQL_DB = "onlyfile"
$env:ALLOW_INSERT_OPERATION = "true"
$env:ALLOW_UPDATE_OPERATION = "true"
$env:ALLOW_DELETE_OPERATION = "false"
$env:MYSQL_ENABLE_LOGGING = "true"
$env:IS_REMOTE_MCP = "true"
$env:PORT = "3000"
$env:REMOTE_SECRET_KEY = "mcp-mysql-secret-918123440"

Write-Host "启动MCP MySQL远程服务..." -ForegroundColor Green
Write-Host "端口: 3000" -ForegroundColor Cyan
Write-Host "密钥: mcp-mysql-secret-918123440" -ForegroundColor Cyan
Write-Host ""

npx @benborla29/mcp-server-mysql
