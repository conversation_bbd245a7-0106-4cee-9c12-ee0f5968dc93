-- 分析重复文档的SQL查询脚本

-- 1. 查看所有重复的文档
SELECT 
    original_name,
    COUNT(*) as duplicate_count,
    GROUP_CONCAT(id ORDER BY created_at) as document_ids,
    GROUP_CONCAT(created_at ORDER BY created_at) as created_dates,
    GROUP_CONCAT(version ORDER BY created_at) as versions
FROM filenet_documents 
GROUP BY original_name 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 2. 具体查看poems_answer.docx的重复情况
SELECT 
    id,
    original_name,
    fn_doc_id,
    file_size,
    version,
    file_hash,
    created_at,
    created_by
FROM filenet_documents 
WHERE original_name = 'poems_answer.docx'
ORDER BY created_at ASC;

-- 3. 查看张艺晗数学错题集的重复情况
SELECT 
    id,
    original_name,
    fn_doc_id,
    file_size,
    version,
    file_hash,
    created_at,
    created_by
FROM filenet_documents 
WHERE original_name LIKE '张艺晗数学错题集%'
ORDER BY created_at ASC;

-- 4. 统计当前数据
SELECT 
    '主文档总数' as item,
    COUNT(*) as count 
FROM filenet_documents
UNION ALL
SELECT 
    '版本记录总数' as item,
    COUNT(*) as count 
FROM filenet_document_versions
UNION ALL
SELECT 
    '重复文档组数' as item,
    COUNT(*) as count 
FROM (
    SELECT original_name
    FROM filenet_documents 
    GROUP BY original_name 
    HAVING COUNT(*) > 1
) as duplicates; 