import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DatabaseService } from '../../database/services/database.service';
import { FilenetService } from '../../filenet/services/filenet.service';
import { FilenetDocumentRow } from '../../database/types/database.types';
import * as fetch from 'node-fetch';
import * as crypto from 'crypto';

/**
 * 文档查询结果接口
 */
interface _DocumentQueryResult extends FilenetDocumentRow {
  // 文档查询可能包含的额外字段
}

/**
 * 文档服务
 * 迁移自原有的services/document.js
 * 提供文档管理的核心功能，保持原有逻辑不变
 */
@Injectable()
export class DocumentService {
  private readonly logger = new Logger(DocumentService.name);
  
  // 用户关闭意图跟踪 - 记录哪些文档是用户主动选择不保存的
  private userCloseIntents = new Map<string, 'save' | 'no-save'>();

  constructor(
    private databaseService: DatabaseService,
    private configService: ConfigService,
    private filenetService: FilenetService,
  ) {}

  /**
   * 设置用户关闭意图
   * @param documentId 文档ID
   * @param intent 用户意图：'save' 表示要保存，'no-save' 表示不保存
   */
  setUserCloseIntent(documentId: string, intent: 'save' | 'no-save'): void {
    console.log(`[DocumentService] 设置文档 ${documentId} 的关闭意图: ${intent}`);
    this.userCloseIntents.set(documentId, intent);
    
    // 5分钟后自动清除意图记录（防止内存泄漏）
    setTimeout(() => {
      this.userCloseIntents.delete(documentId);
      console.log(`[DocumentService] 自动清除文档 ${documentId} 的关闭意图记录`);
    }, 5 * 60 * 1000);
  }

  /**
   * 获取用户关闭意图
   */
  private getUserCloseIntent(documentId: string): 'save' | 'no-save' | undefined {
    return this.userCloseIntents.get(documentId);
  }

  /**
   * 获取文档列表
   * @param options 查询选项
   * @returns 文档列表
   */
  async getDocumentList(options: {
    search?: string;
    extension?: string;
    limit?: number;
    offset?: number;
  } = {}) {
    try {
      console.log('[DocumentService] 获取文档列表，选项:', options);
      
      // 从数据库获取文档数据 (保持原有逻辑)
      let query = `
        SELECT 
          id, fn_doc_id, original_name, file_size, mime_type, extension,
          version, file_hash, created_by, last_modified_by, template_id,
          uploaded_at, created_at, updated_at 
        FROM filenet_documents 
        WHERE is_deleted = FALSE
      `;
      
      const params = [];
      
      // 添加查询条件
      if (options.search) {
        query += ` AND original_name LIKE ?`;
        params.push(`%${options.search}%`);
      }
      
      if (options.extension) {
        query += ` AND extension = ?`;
        params.push(options.extension);
      }
      
      // 排序
      query += ` ORDER BY created_at DESC`;
      
      // 分页
      if (options.limit) {
        query += ` LIMIT ?`;
        params.push(options.limit);
        
        if (options.offset) {
          query += ` OFFSET ?`;
          params.push(options.offset);
        }
      }
      
      // 获取总数（用于分页）
      let countQuery = `
        SELECT COUNT(*) as total
        FROM filenet_documents 
        WHERE is_deleted = FALSE
      `;
      
      const countParams = [];
      
      // 添加相同的查询条件用于计数
      if (options.search) {
        countQuery += ` AND original_name LIKE ?`;
        countParams.push(`%${options.search}%`);
      }
      
      if (options.extension) {
        countQuery += ` AND extension = ?`;
        countParams.push(options.extension);
      }
      
      console.log('🔍 [Debug] 总数查询SQL:', countQuery);
      console.log('🔍 [Debug] 总数查询参数:', countParams);
      
      const countResult = await this.databaseService.queryOne(countQuery, countParams) as { total: number };
      console.log('🔍 [Debug] 总数查询结果:', countResult);
      
      const total = countResult?.total || 0;
      console.log('🔍 [Debug] 最终总数:', total);
      
      const documents = await this.databaseService.query(query, params) as unknown as FilenetDocumentRow[];
      
      // 转换为前端需要的格式 (保持原有逻辑)
      const result = documents.map(file => ({
        id: file.id,
        name: file.original_name,
        type: file.extension,
        size: file.file_size,
        lastModified: file.updated_at,
        url: `/api/documents/${file.id}`,
        editUrl: `/editor/${file.id}`,
        version: file.version,
        createdAt: file.created_at,
        createdBy: file.created_by,
        modifiedBy: file.last_modified_by,
        fileHash: file.file_hash,
        fnDocId: file.fn_doc_id,
        templateId: file.template_id
      }));
      
      console.log(`[DocumentService] 获取到 ${result.length} 个文档，总计: ${total}`);
      return {
        data: result,
        total: total,
        page: options.offset ? Math.floor(options.offset / (options.limit || 20)) + 1 : 1,
        limit: options.limit || 20
      };
      
    } catch (error) {
      console.error('[DocumentService] 获取文档列表失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取文档详情
   * @param documentId 文档ID
   * @returns 文档详情
   */
  async getDocumentById(documentId: string) {
    try {
      console.log('[DocumentService] 获取文档详情，ID:', documentId);
      
      const document = await this.databaseService.queryOne(
        `SELECT * FROM filenet_documents WHERE id = ? AND is_deleted = FALSE`,
        [documentId]
      ) as unknown as FilenetDocumentRow;
      
      if (!document) {
        throw new Error('文档不存在');
      }
      
      return {
        id: document.id,
        name: document.original_name,
        type: document.extension,
        size: document.file_size,
        mimeType: document.mime_type,
        version: document.version,
        fileHash: document.file_hash,
        createdBy: document.created_by,
        lastModifiedBy: document.last_modified_by,
        templateId: document.template_id,
        fnDocId: document.fn_doc_id,
        uploadedAt: document.uploaded_at,
        createdAt: document.created_at,
        updatedAt: document.updated_at
      };
      
    } catch (error) {
      console.error('[DocumentService] 获取文档详情失败:', error);
      throw error;
    }
  }

  /**
   * 获取文档配置 (OnlyOffice编辑器配置)
   * @param documentId 文档ID
   * @returns OnlyOffice文档配置
   */
  async getDocumentConfig(documentId: string) {
    try {
      console.log('[DocumentService] 获取文档配置，ID:', documentId);
      
      const document = await this.getDocumentById(documentId);
      
      // 创建可供OnlyOffice访问的绝对URL
      const serverHost = this.configService.get<string>('SERVER_HOST', 'localhost');
      const serverPort = this.configService.get<string>('PORT', '3000');
      const fileUrl = `http://${serverHost}:${serverPort}/api/documents/${documentId}/download`;
      
      console.log('[DocumentService] OnlyOffice将使用的URL:', fileUrl);
      
      const fileExt = document.type.toLowerCase();
      const fileKey = `${documentId}-${Date.now()}`;
      
      // 确定文档类型 - 保持原有逻辑
      let documentType = 'word';
      if (['xlsx', 'xls'].includes(fileExt)) {
        documentType = 'cell';
      } else if (['pptx', 'ppt'].includes(fileExt)) {
        documentType = 'slide';
      } else if (fileExt === 'pdf') {
        documentType = 'pdf';
      }
      
      console.log('[DocumentService] 文档类型:', documentType, '文件扩展名:', fileExt);
      
      // 获取回调URL配置
      const callbackUrl = this.configService.get<string>('CALLBACK_URL', `http://${serverHost}:${serverPort}/api/documents/callback`);
      
      // 创建文档配置 - 保持原有逻辑
      const docConfig = {
        document: {
          fileType: fileExt,
          key: fileKey,
          title: document.name,
          url: fileUrl,
          permissions: {
            edit: fileExt !== 'pdf', // PDF只能查看
            download: true,
            print: true,
            comment: true,
            chat: false,
            review: true,
            copy: true
          }
        },
        documentType: documentType,
        editorConfig: {
          callbackUrl: callbackUrl,
          lang: 'zh',
          mode: fileExt === 'pdf' ? 'view' : 'edit', // PDF只能查看
          customization: {
            compactHeader: false,
            hideRightMenu: false,
            forcesave: true,
            autosave: true,
            help: true,
            about: true,
            zoom: 100,
            uiTheme: 'theme-light'
          },
          user: {
            id: 'user-1',
            name: 'OnlyOffice用户',
            group: 'editors'
          },
          coEditing: {
            mode: "fast",
            change: true
          }
        }
      };
      
      console.log('[DocumentService] 生成OnlyOffice配置成功');
      return docConfig;
      
    } catch (error) {
      console.error('[DocumentService] 获取文档配置失败:', error);
      throw error;
    }
  }

  /**
   * 删除文档 (软删除)
   * @param documentId 文档ID
   * @returns 是否删除成功
   */
  async deleteDocument(documentId: string): Promise<boolean> {
    try {
      console.log('[DocumentService] 请求删除文档ID:', documentId);
      
      // 查询文件信息
      const fileInfo = await this.databaseService.queryOne(
        'SELECT id, fn_doc_id FROM filenet_documents WHERE id = ? AND is_deleted = FALSE',
        [documentId]
      ) as unknown as FilenetDocumentRow;
      
      if (!fileInfo) {
        console.error('[DocumentService] 找不到要删除的文件:', documentId);
        return false;
      }
      
      // 软删除文件记录
      await this.databaseService.query(
        'UPDATE filenet_documents SET is_deleted = TRUE WHERE id = ?',
        [documentId]
      );
      
      console.log(`[DocumentService] 文件已标记为删除: ID=${documentId}, FileNet DocID=${fileInfo.fn_doc_id}`);
      return true;
      
    } catch (error) {
      console.error('[DocumentService] 删除文件失败:', error);
      throw error;
    }
  }

  /**
   * 处理OnlyOffice保存回调
   * @param callbackData 回调数据
   * @returns 处理结果
   */
  async handleCallback(callbackData: Record<string, unknown>) {
    try {
      console.log(`[DocumentService] 收到OnlyOffice回调请求: status=${callbackData.status}, key=${callbackData.key}`);
      
      // 状态说明:
      // 0 - 找不到带有标识符的文档
      // 1 - 正在编辑文档文件
      // 2 - 已准备好保存
      // 3 - 发生了文档保存错误
      // 4 - 用户关闭文档，没有变化
      // 6 - 正在编辑文档，但保存当前文档状态
      // 7 - 强制保存文档时发生错误
      
      if ((callbackData.status === 2 || callbackData.status === 6) && callbackData.url) {
        console.log(`[DocumentService] 文档状态 ${callbackData.status}，URL: ${callbackData.url}`);

        try {
          // 从回调key中提取文档ID (key格式: documentId-timestamp)
          const key = callbackData.key as string;
          // UUID格式：xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx-timestamp
          // 需要提取完整的UUID部分，不是简单的split('-')[0]
          const uuidRegex = /^([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})-\d+$/i;
          const match = key?.match(uuidRegex);
          const documentId = match ? match[1] : null;
          
          if (!documentId) {
            console.error('[DocumentService] 无法从key中提取文档ID:', key);
            return { error: 1 };
          }
          
          console.log(`[DocumentService] 提取到文档ID: ${documentId}`);
          
          // 检查用户关闭意图
          const userIntent = this.getUserCloseIntent(documentId);
          if (userIntent === 'no-save') {
            console.log(`[DocumentService] 用户选择了直接关闭不保存，忽略此次回调保存`);
            // 清除意图记录
            this.userCloseIntents.delete(documentId);
            return { error: 0 }; // 返回成功，但不实际保存
          }
          
          if (userIntent === 'save') {
            console.log(`[DocumentService] 用户选择了保存并关闭，继续执行保存流程`);
            // 清除意图记录
            this.userCloseIntents.delete(documentId);
          } else {
            console.log(`[DocumentService] 没有明确的用户意图记录，按默认流程处理（正常的自动保存）`);
          }
          
          // 获取文档信息
          const document = await this.getDocumentById(documentId);
          if (!document) {
            console.error(`[DocumentService] 找不到文档: ${documentId}`);
            return { error: 1 };
          }
          
          console.log(`[DocumentService] 找到文档: ${document.name}, FileNet ID: ${document.fnDocId}`);
          
          // 1. 从OnlyOffice回调URL下载更新的文档
          const documentUrl = callbackData.url as string;
          console.log(`[DocumentService] 开始从OnlyOffice下载文档: ${documentUrl}`);
          
          const response = await fetch.default(documentUrl);
          
          if (!response.ok) {
            throw new Error(`下载文档失败: ${response.status} ${response.statusText}`);
          }
          
          const documentBuffer = await response.buffer();
          console.log(`[DocumentService] 下载文档成功，大小: ${documentBuffer.length} 字节`);
          
          // 计算文件哈希值用于版本管理
          const fileHash = crypto.createHash('sha256').update(documentBuffer).digest('hex');
          console.log(`[DocumentService] 计算文件哈希: ${fileHash}`);
          
          // 2. 将新版本上传到FileNet作为新版本（不创建新的文档记录）
          let newFileNetId: string | null = null;
          if (document.fnDocId) {
            console.log(`[DocumentService] 开始上传新版本到FileNet`);
            
            try {
              // 直接调用FileNet上传，不创建数据库记录
              newFileNetId = await this.filenetService.uploadFileToFileNetOnly(
                documentBuffer,
                document.name,
                document.mimeType,
                'onlyoffice-system'
              );
              
              console.log(`[DocumentService] 新版本上传到FileNet成功, FileNet ID: ${newFileNetId}`);
              
            } catch (fileNetError) {
              console.error(`[DocumentService] FileNet版本上传失败:`, fileNetError);
              // 如果FileNet上传失败，仍然可以继续保存版本记录（不依赖FileNet）
              newFileNetId = null;
            }
          } else {
            console.warn(`[DocumentService] 文档没有FileNet ID，跳过FileNet版本上传`);
          }
          
          // 3. 计算新版本号
          const newVersion = (document.version || 0) + 1;
          console.log(`[DocumentService] 新版本号: ${newVersion}`);
          
          // 4. 将新版本保存到版本表 filenet_document_versions
          const versionId = this.generateUUID();
          const now = new Date().toISOString().slice(0, 19).replace('T', ' ');

          try {
            await this.databaseService.query(
              `INSERT INTO filenet_document_versions 
               (id, doc_id, fn_doc_id, version, file_hash, modified_by, modified_at, file_size, comment) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                versionId,
                documentId,
                newFileNetId || document.fnDocId, // 使用新的FileNet ID，如果没有则使用原ID
                newVersion,
                fileHash,
                'onlyoffice-system',
                now,
                documentBuffer.length,
                `OnlyOffice编辑更新 (status ${callbackData.status})`
              ]
            );
            console.log(`[DocumentService] 版本记录保存成功: ${versionId}, 版本号: ${newVersion}`);
          } catch (versionError) {
            console.error(`[DocumentService] 保存版本记录失败:`, versionError);
            // 版本记录保存失败不应该影响主文档更新，继续执行
          }
          
          // 5. 更新主文档表的元数据（版本号、文件大小、更新时间）
          // 注意：不要更新 fn_doc_id，保持主文档的FileNet ID不变
          await this.databaseService.query(
            `UPDATE filenet_documents
             SET version = ?, file_size = ?, updated_at = ?, last_modified_by = ?
             WHERE id = ?`,
            [newVersion, documentBuffer.length, now, 'onlyoffice-system', documentId]
          );

          console.log(`[DocumentService] 主文档表更新成功，新版本: ${newVersion}，文件大小: ${documentBuffer.length}`);
          
          // 6. 如果有历史记录或变更日志URL，可以进一步处理
          if (callbackData.changesurl) {
            console.log(`[DocumentService] 检测到变更记录URL: ${callbackData.changesurl}`);
            // TODO: 可以下载并保存变更历史
          }
          
          if (callbackData.history) {
            console.log(`[DocumentService] 检测到历史记录:`, callbackData.history);
            // TODO: 可以保存编辑历史到数据库
          }
          
          console.log('[DocumentService] 文档版本保存处理完成');
          return { error: 0 }; // 返回成功状态
          
        } catch (saveError) {
          console.error('[DocumentService] 文档保存处理失败:', saveError);
          return { error: 1 }; // 返回错误状态
        }
      }
      
      if (callbackData.status === 1) {
        console.log('[DocumentService] 文档正在编辑中');
        return { error: 0 };
      }
      
      if (callbackData.status === 4) {
        console.log('[DocumentService] 用户关闭文档，无变化');
        return { error: 0 };
      }
      
      console.log('[DocumentService] 其他状态，无需处理');
      return { error: 0 };
      
    } catch (error) {
      console.error('[DocumentService] 处理回调失败:', error);
      return { error: 1 }; // 返回错误状态
    }
  }

  /**
   * 创建文档记录
   * @param documentData 文档数据
   * @returns 文档ID
   */
  async createDocumentRecord(documentData: Record<string, unknown>): Promise<string> {
    try {
      console.log('[DocumentService] 创建文档记录:', documentData);
      
      const documentId = this.generateUUID();
      
      await this.databaseService.query(
        `INSERT INTO filenet_documents 
         (id, fn_doc_id, original_name, file_size, mime_type, extension, 
          version, file_hash, created_by, template_id) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          documentId,
          documentData.fnDocId || '',
          documentData.originalName,
          documentData.fileSize || 0,
          documentData.mimeType || '',
          documentData.extension || '',
          documentData.version || 1,
          documentData.fileHash || '',
          documentData.createdBy || 'system',
          documentData.templateId || null
        ]
      );
      
      console.log('[DocumentService] 文档记录创建成功，ID:', documentId);
      return documentId;
      
    } catch (error) {
      console.error('[DocumentService] 创建文档记录失败:', error);
      throw error;
    }
  }

  /**
   * 从FileNet下载文档
   * @param fnDocId FileNet文档ID
   * @returns 下载结果包含文档流
   */
  async downloadFromFileNet(fnDocId: string) {
    try {
      console.log(`[DocumentService] 从FileNet下载文档: ${fnDocId}`);
      return await this.filenetService.downloadDocument(fnDocId);
    } catch (error) {
      console.error(`[DocumentService] 从FileNet下载文档失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取文档的版本历史
   * @param documentId 文档ID
   * @returns 版本列表
   */
  async getDocumentVersions(documentId: string): Promise<unknown[]> {
    try {
      console.log(`[DocumentService] 获取文档版本历史: ${documentId}`);
      
      const versions = await this.databaseService.query(
        `SELECT 
           id,
           doc_id,
           fn_doc_id,
           version,
           file_hash,
           modified_by,
           modified_at,
           file_size,
           comment
         FROM filenet_document_versions 
         WHERE doc_id = ? 
         ORDER BY version DESC`,
        [documentId]
      ) as unknown[];
      
      console.log(`[DocumentService] 找到 ${versions.length} 个版本`);
      return versions;
    } catch (error) {
      console.error(`[DocumentService] 获取文档版本失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取文档的特定版本内容
   * @param documentId 文档ID
   * @param version 版本号
   * @returns 版本内容
   */
  async getDocumentVersionContent(documentId: string, version: number) {
    try {
      console.log(`[DocumentService] 获取文档版本内容: ${documentId}, 版本: ${version}`);
      
      // 首先获取版本记录
      const versionRecord = await this.databaseService.queryOne(
        `SELECT 
           id,
           doc_id,
           fn_doc_id,
           version,
           file_hash,
           modified_by,
           modified_at,
           file_size,
           comment
         FROM filenet_document_versions 
         WHERE doc_id = ? AND version = ?
         LIMIT 1`,
        [documentId, version]
      ) as Record<string, unknown>;
      
      if (!versionRecord) {
        throw new Error(`找不到文档版本: ${documentId} v${version}`);
      }
      
      // 获取主文档信息（用于文件名等）
      const document = await this.getDocumentById(documentId);
      if (!document) {
        throw new Error(`找不到主文档: ${documentId}`);
      }
      
      // 如果版本有FileNet ID，从FileNet下载
      if (versionRecord.fn_doc_id) {
        console.log(`[DocumentService] 从FileNet下载版本内容: ${versionRecord.fn_doc_id}`);
        const downloadResult = await this.filenetService.downloadDocument(versionRecord.fn_doc_id as string);
        
        return {
          versionInfo: versionRecord,
          documentInfo: document,
          content: downloadResult
        };
      } else {
        throw new Error(`版本 ${version} 没有关联的FileNet文档`);
      }
      
    } catch (error) {
      console.error(`[DocumentService] 获取文档版本内容失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 生成UUID
   */
  private generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
} 