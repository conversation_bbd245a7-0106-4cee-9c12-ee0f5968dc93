"use strict";
exports.id = 0;
exports.ids = null;
exports.modules = {

/***/ 49:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DocumentService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.DocumentService = void 0;
const common_1 = __webpack_require__(5);
const config_1 = __webpack_require__(8);
const database_service_1 = __webpack_require__(15);
const filenet_service_1 = __webpack_require__(50);
const fetch = __webpack_require__(55);
const crypto = __webpack_require__(53);
let DocumentService = DocumentService_1 = class DocumentService {
    constructor(databaseService, configService, filenetService) {
        this.databaseService = databaseService;
        this.configService = configService;
        this.filenetService = filenetService;
        this.logger = new common_1.Logger(DocumentService_1.name);
        this.userCloseIntents = new Map();
    }
    setUserCloseIntent(documentId, intent) {
        console.log(`[DocumentService] 设置文档 ${documentId} 的关闭意图: ${intent}`);
        this.userCloseIntents.set(documentId, intent);
        setTimeout(() => {
            this.userCloseIntents.delete(documentId);
            console.log(`[DocumentService] 自动清除文档 ${documentId} 的关闭意图记录`);
        }, 5 * 60 * 1000);
    }
    getUserCloseIntent(documentId) {
        return this.userCloseIntents.get(documentId);
    }
    async getDocumentList(options = {}) {
        try {
            console.log('[DocumentService] 获取文档列表，选项:', options);
            let query = `
        SELECT 
          id, fn_doc_id, original_name, file_size, mime_type, extension,
          version, file_hash, created_by, last_modified_by, template_id,
          uploaded_at, created_at, updated_at 
        FROM filenet_documents 
        WHERE is_deleted = FALSE
      `;
            const params = [];
            if (options.search) {
                query += ` AND original_name LIKE ?`;
                params.push(`%${options.search}%`);
            }
            if (options.extension) {
                query += ` AND extension = ?`;
                params.push(options.extension);
            }
            query += ` ORDER BY created_at DESC`;
            if (options.limit) {
                query += ` LIMIT ?`;
                params.push(options.limit);
                if (options.offset) {
                    query += ` OFFSET ?`;
                    params.push(options.offset);
                }
            }
            let countQuery = `
        SELECT COUNT(*) as total
        FROM filenet_documents 
        WHERE is_deleted = FALSE
      `;
            const countParams = [];
            if (options.search) {
                countQuery += ` AND original_name LIKE ?`;
                countParams.push(`%${options.search}%`);
            }
            if (options.extension) {
                countQuery += ` AND extension = ?`;
                countParams.push(options.extension);
            }
            console.log('🔍 [Debug] 总数查询SQL:', countQuery);
            console.log('🔍 [Debug] 总数查询参数:', countParams);
            const countResult = await this.databaseService.queryOne(countQuery, countParams);
            console.log('🔍 [Debug] 总数查询结果:', countResult);
            const total = countResult?.total || 0;
            console.log('🔍 [Debug] 最终总数:', total);
            const documents = await this.databaseService.query(query, params);
            const result = documents.map(file => ({
                id: file.id,
                name: file.original_name,
                type: file.extension,
                size: file.file_size,
                lastModified: file.updated_at,
                url: `/api/documents/${file.id}`,
                editUrl: `/editor/${file.id}`,
                version: file.version,
                createdAt: file.created_at,
                createdBy: file.created_by,
                modifiedBy: file.last_modified_by,
                fileHash: file.file_hash,
                fnDocId: file.fn_doc_id,
                templateId: file.template_id
            }));
            console.log(`[DocumentService] 获取到 ${result.length} 个文档，总计: ${total}`);
            return {
                data: result,
                total: total,
                page: options.offset ? Math.floor(options.offset / (options.limit || 20)) + 1 : 1,
                limit: options.limit || 20
            };
        }
        catch (error) {
            console.error('[DocumentService] 获取文档列表失败:', error);
            throw error;
        }
    }
    async getDocumentById(documentId) {
        try {
            console.log('[DocumentService] 获取文档详情，ID:', documentId);
            const document = await this.databaseService.queryOne(`SELECT * FROM filenet_documents WHERE id = ? AND is_deleted = FALSE`, [documentId]);
            if (!document) {
                throw new Error('文档不存在');
            }
            return {
                id: document.id,
                name: document.original_name,
                type: document.extension,
                size: document.file_size,
                mimeType: document.mime_type,
                version: document.version,
                fileHash: document.file_hash,
                createdBy: document.created_by,
                lastModifiedBy: document.last_modified_by,
                templateId: document.template_id,
                fnDocId: document.fn_doc_id,
                uploadedAt: document.uploaded_at,
                createdAt: document.created_at,
                updatedAt: document.updated_at
            };
        }
        catch (error) {
            console.error('[DocumentService] 获取文档详情失败:', error);
            throw error;
        }
    }
    async getDocumentConfig(documentId) {
        try {
            console.log('[DocumentService] 获取文档配置，ID:', documentId);
            const document = await this.getDocumentById(documentId);
            const serverHost = this.configService.get('SERVER_HOST', 'localhost');
            const serverPort = this.configService.get('PORT', '3000');
            const fileUrl = `http://${serverHost}:${serverPort}/api/documents/${documentId}/download`;
            console.log('[DocumentService] OnlyOffice将使用的URL:', fileUrl);
            const fileExt = document.type.toLowerCase();
            const fileKey = `${documentId}-${Date.now()}`;
            let documentType = 'word';
            if (['xlsx', 'xls'].includes(fileExt)) {
                documentType = 'cell';
            }
            else if (['pptx', 'ppt'].includes(fileExt)) {
                documentType = 'slide';
            }
            else if (fileExt === 'pdf') {
                documentType = 'pdf';
            }
            console.log('[DocumentService] 文档类型:', documentType, '文件扩展名:', fileExt);
            const callbackUrl = this.configService.get('CALLBACK_URL', `http://${serverHost}:${serverPort}/api/documents/callback`);
            const docConfig = {
                document: {
                    fileType: fileExt,
                    key: fileKey,
                    title: document.name,
                    url: fileUrl,
                    permissions: {
                        edit: fileExt !== 'pdf',
                        download: true,
                        print: true,
                        comment: true,
                        chat: false,
                        review: true,
                        copy: true
                    }
                },
                documentType: documentType,
                editorConfig: {
                    callbackUrl: callbackUrl,
                    lang: 'zh',
                    mode: fileExt === 'pdf' ? 'view' : 'edit',
                    customization: {
                        compactHeader: false,
                        hideRightMenu: false,
                        forcesave: true,
                        autosave: true,
                        help: true,
                        about: true,
                        zoom: 100,
                        uiTheme: 'theme-light'
                    },
                    user: {
                        id: 'user-1',
                        name: 'OnlyOffice用户',
                        group: 'editors'
                    },
                    coEditing: {
                        mode: "fast",
                        change: true
                    }
                }
            };
            console.log('[DocumentService] 生成OnlyOffice配置成功');
            return docConfig;
        }
        catch (error) {
            console.error('[DocumentService] 获取文档配置失败:', error);
            throw error;
        }
    }
    async deleteDocument(documentId) {
        try {
            console.log('[DocumentService] 请求删除文档ID:', documentId);
            const fileInfo = await this.databaseService.queryOne('SELECT id, fn_doc_id FROM filenet_documents WHERE id = ? AND is_deleted = FALSE', [documentId]);
            if (!fileInfo) {
                console.error('[DocumentService] 找不到要删除的文件:', documentId);
                return false;
            }
            await this.databaseService.query('UPDATE filenet_documents SET is_deleted = TRUE WHERE id = ?', [documentId]);
            console.log(`[DocumentService] 文件已标记为删除: ID=${documentId}, FileNet DocID=${fileInfo.fn_doc_id}`);
            return true;
        }
        catch (error) {
            console.error('[DocumentService] 删除文件失败:', error);
            throw error;
        }
    }
    async handleCallback(callbackData) {
        try {
            console.log(`[DocumentService] 收到OnlyOffice回调请求: status=${callbackData.status}, key=${callbackData.key}`);
            if ((callbackData.status === 2 || callbackData.status === 6) && callbackData.url) {
                console.log(`[DocumentService] 文档状态 ${callbackData.status}，URL: ${callbackData.url}`);
                try {
                    const key = callbackData.key;
                    const uuidRegex = /^([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})-\d+$/i;
                    const match = key?.match(uuidRegex);
                    const documentId = match ? match[1] : null;
                    if (!documentId) {
                        console.error('[DocumentService] 无法从key中提取文档ID:', key);
                        return { error: 1 };
                    }
                    console.log(`[DocumentService] 提取到文档ID: ${documentId}`);
                    const userIntent = this.getUserCloseIntent(documentId);
                    if (userIntent === 'no-save') {
                        console.log(`[DocumentService] 用户选择了直接关闭不保存，忽略此次回调保存`);
                        this.userCloseIntents.delete(documentId);
                        return { error: 0 };
                    }
                    if (userIntent === 'save') {
                        console.log(`[DocumentService] 用户选择了保存并关闭，继续执行保存流程`);
                        this.userCloseIntents.delete(documentId);
                    }
                    else {
                        console.log(`[DocumentService] 没有明确的用户意图记录，按默认流程处理（正常的自动保存）`);
                    }
                    const document = await this.getDocumentById(documentId);
                    if (!document) {
                        console.error(`[DocumentService] 找不到文档: ${documentId}`);
                        return { error: 1 };
                    }
                    console.log(`[DocumentService] 找到文档: ${document.name}, FileNet ID: ${document.fnDocId}`);
                    const documentUrl = callbackData.url;
                    console.log(`[DocumentService] 开始从OnlyOffice下载文档: ${documentUrl}`);
                    const response = await fetch.default(documentUrl);
                    if (!response.ok) {
                        throw new Error(`下载文档失败: ${response.status} ${response.statusText}`);
                    }
                    const documentBuffer = await response.buffer();
                    console.log(`[DocumentService] 下载文档成功，大小: ${documentBuffer.length} 字节`);
                    const fileHash = crypto.createHash('sha256').update(documentBuffer).digest('hex');
                    console.log(`[DocumentService] 计算文件哈希: ${fileHash}`);
                    let newFileNetId = null;
                    if (document.fnDocId) {
                        console.log(`[DocumentService] 开始上传新版本到FileNet`);
                        try {
                            newFileNetId = await this.filenetService.uploadFileToFileNetOnly(documentBuffer, document.name, document.mimeType, 'onlyoffice-system');
                            console.log(`[DocumentService] 新版本上传到FileNet成功, FileNet ID: ${newFileNetId}`);
                        }
                        catch (fileNetError) {
                            console.error(`[DocumentService] FileNet版本上传失败:`, fileNetError);
                            newFileNetId = null;
                        }
                    }
                    else {
                        console.warn(`[DocumentService] 文档没有FileNet ID，跳过FileNet版本上传`);
                    }
                    const newVersion = (document.version || 0) + 1;
                    console.log(`[DocumentService] 新版本号: ${newVersion}`);
                    const versionId = this.generateUUID();
                    const now = new Date().toISOString().slice(0, 19).replace('T', ' ');
                    try {
                        await this.databaseService.query(`INSERT INTO filenet_document_versions 
               (id, doc_id, fn_doc_id, version, file_hash, modified_by, modified_at, file_size, comment) 
               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
                            versionId,
                            documentId,
                            newFileNetId || document.fnDocId,
                            newVersion,
                            fileHash,
                            'onlyoffice-system',
                            now,
                            documentBuffer.length,
                            `OnlyOffice编辑更新 (status ${callbackData.status})`
                        ]);
                        console.log(`[DocumentService] 版本记录保存成功: ${versionId}, 版本号: ${newVersion}`);
                    }
                    catch (versionError) {
                        console.error(`[DocumentService] 保存版本记录失败:`, versionError);
                    }
                    await this.databaseService.query(`UPDATE filenet_documents
             SET version = ?, file_size = ?, updated_at = ?, last_modified_by = ?
             WHERE id = ?`, [newVersion, documentBuffer.length, now, 'onlyoffice-system', documentId]);
                    console.log(`[DocumentService] 主文档表更新成功，新版本: ${newVersion}，文件大小: ${documentBuffer.length}`);
                    if (callbackData.changesurl) {
                        console.log(`[DocumentService] 检测到变更记录URL: ${callbackData.changesurl}`);
                    }
                    if (callbackData.history) {
                        console.log(`[DocumentService] 检测到历史记录:`, callbackData.history);
                    }
                    console.log('[DocumentService] 文档版本保存处理完成');
                    return { error: 0 };
                }
                catch (saveError) {
                    console.error('[DocumentService] 文档保存处理失败:', saveError);
                    return { error: 1 };
                }
            }
            if (callbackData.status === 1) {
                console.log('[DocumentService] 文档正在编辑中');
                return { error: 0 };
            }
            if (callbackData.status === 4) {
                console.log('[DocumentService] 用户关闭文档，无变化');
                return { error: 0 };
            }
            console.log('[DocumentService] 其他状态，无需处理');
            return { error: 0 };
        }
        catch (error) {
            console.error('[DocumentService] 处理回调失败:', error);
            return { error: 1 };
        }
    }
    async createDocumentRecord(documentData) {
        try {
            console.log('[DocumentService] 创建文档记录:', documentData);
            const documentId = this.generateUUID();
            await this.databaseService.query(`INSERT INTO filenet_documents 
         (id, fn_doc_id, original_name, file_size, mime_type, extension, 
          version, file_hash, created_by, template_id) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, [
                documentId,
                documentData.fnDocId || '',
                documentData.originalName,
                documentData.fileSize || 0,
                documentData.mimeType || '',
                documentData.extension || '',
                documentData.version || 1,
                documentData.fileHash || '',
                documentData.createdBy || 'system',
                documentData.templateId || null
            ]);
            console.log('[DocumentService] 文档记录创建成功，ID:', documentId);
            return documentId;
        }
        catch (error) {
            console.error('[DocumentService] 创建文档记录失败:', error);
            throw error;
        }
    }
    async downloadFromFileNet(fnDocId) {
        try {
            console.log(`[DocumentService] 从FileNet下载文档: ${fnDocId}`);
            return await this.filenetService.downloadDocument(fnDocId);
        }
        catch (error) {
            console.error(`[DocumentService] 从FileNet下载文档失败: ${error.message}`);
            throw error;
        }
    }
    async getDocumentVersions(documentId) {
        try {
            console.log(`[DocumentService] 获取文档版本历史: ${documentId}`);
            const versions = await this.databaseService.query(`SELECT 
           id,
           doc_id,
           fn_doc_id,
           version,
           file_hash,
           modified_by,
           modified_at,
           file_size,
           comment
         FROM filenet_document_versions 
         WHERE doc_id = ? 
         ORDER BY version DESC`, [documentId]);
            console.log(`[DocumentService] 找到 ${versions.length} 个版本`);
            return versions;
        }
        catch (error) {
            console.error(`[DocumentService] 获取文档版本失败: ${error.message}`);
            throw error;
        }
    }
    async getDocumentVersionContent(documentId, version) {
        try {
            console.log(`[DocumentService] 获取文档版本内容: ${documentId}, 版本: ${version}`);
            const versionRecord = await this.databaseService.queryOne(`SELECT 
           id,
           doc_id,
           fn_doc_id,
           version,
           file_hash,
           modified_by,
           modified_at,
           file_size,
           comment
         FROM filenet_document_versions 
         WHERE doc_id = ? AND version = ?
         LIMIT 1`, [documentId, version]);
            if (!versionRecord) {
                throw new Error(`找不到文档版本: ${documentId} v${version}`);
            }
            const document = await this.getDocumentById(documentId);
            if (!document) {
                throw new Error(`找不到主文档: ${documentId}`);
            }
            if (versionRecord.fn_doc_id) {
                console.log(`[DocumentService] 从FileNet下载版本内容: ${versionRecord.fn_doc_id}`);
                const downloadResult = await this.filenetService.downloadDocument(versionRecord.fn_doc_id);
                return {
                    versionInfo: versionRecord,
                    documentInfo: document,
                    content: downloadResult
                };
            }
            else {
                throw new Error(`版本 ${version} 没有关联的FileNet文档`);
            }
        }
        catch (error) {
            console.error(`[DocumentService] 获取文档版本内容失败: ${error.message}`);
            throw error;
        }
    }
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
};
exports.DocumentService = DocumentService;
exports.DocumentService = DocumentService = DocumentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof database_service_1.DatabaseService !== "undefined" && database_service_1.DatabaseService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof filenet_service_1.FilenetService !== "undefined" && filenet_service_1.FilenetService) === "function" ? _c : Object])
], DocumentService);


/***/ })

};
exports.runtime =
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("53843ebae77c5225bb08")
/******/ })();
/******/ 
/******/ }
;