# 测试新的MCP配置
Write-Host "测试新的MCP配置" -ForegroundColor Green
Write-Host "=================" -ForegroundColor Green
Write-Host ""

# 设置环境变量
$env:MYSQL_HOST = "*************"
$env:MYSQL_PORT = "3306"
$env:MYSQL_USER = "onlyfile_user"
$env:MYSQL_PASS = "0nlyF!le`$ecure#123"
$env:MYSQL_DB = "onlyfile"
$env:MYSQL_ENABLE_LOGGING = "true"
$env:ALLOW_INSERT_OPERATION = "true"
$env:ALLOW_UPDATE_OPERATION = "true"
$env:ALLOW_DELETE_OPERATION = "false"

Write-Host "1. 测试文档中提到的dotenv解决方案..." -ForegroundColor Yellow
try {
    Write-Host "   运行: npx -y -p @benborla29/mcp-server-mysql -p dotenv mcp-server-mysql" -ForegroundColor Cyan
    
    # 创建一个简单的测试
    $job = Start-Job -ScriptBlock {
        param($host, $port, $user, $pass, $db)
        
        $env:MYSQL_HOST = $host
        $env:MYSQL_PORT = $port
        $env:MYSQL_USER = $user
        $env:MYSQL_PASS = $pass
        $env:MYSQL_DB = $db
        $env:MYSQL_ENABLE_LOGGING = "true"
        
        # 使用文档中提到的解决方案
        $output = npx -y -p @benborla29/mcp-server-mysql -p dotenv mcp-server-mysql 2>&1
        return $output
    } -ArgumentList $env:MYSQL_HOST, $env:MYSQL_PORT, $env:MYSQL_USER, $env:MYSQL_PASS, $env:MYSQL_DB
    
    Start-Sleep -Seconds 5
    $result = Receive-Job -Job $job
    Remove-Job -Job $job -Force
    
    if ($result) {
        Write-Host "   输出: $result" -ForegroundColor White
    } else {
        Write-Host "   没有输出（这可能是正常的）" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "2. 检查当前配置文件..." -ForegroundColor Yellow
if (Test-Path ".cursor\mcp.json") {
    $config = Get-Content ".cursor\mcp.json" | ConvertFrom-Json
    Write-Host "   服务器名称: $($config.mcpServers.PSObject.Properties.Name)" -ForegroundColor Cyan
    Write-Host "   命令: $($config.mcpServers.mcp_server_mysql.command)" -ForegroundColor Cyan
    Write-Host "   参数: $($config.mcpServers.mcp_server_mysql.args -join ' ')" -ForegroundColor Cyan
} else {
    Write-Host "   配置文件不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. 尝试直接安装mcp-server-mysql命令..." -ForegroundColor Yellow
try {
    Write-Host "   检查全局安装的包..." -ForegroundColor Cyan
    $globalPackages = npm list -g --depth=0 2>$null
    if ($globalPackages -match "mcp-server-mysql") {
        Write-Host "   ✓ 包已全局安装" -ForegroundColor Green
    } else {
        Write-Host "   包未全局安装，尝试安装..." -ForegroundColor Yellow
        npm install -g @benborla29/mcp-server-mysql
    }
}
catch {
    Write-Host "   安装失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "4. 创建备用配置..." -ForegroundColor Yellow

# 创建一个备用配置文件
$backupConfig = @{
    mcpServers = @{
        mysql = @{
            command = "D:\Program Files\nodejs\node.exe"
            args = @("C:\Users\<USER>\AppData\Roaming\npm\node_modules\@benborla29\mcp-server-mysql\dist\index.js")
            env = @{
                MYSQL_HOST = "*************"
                MYSQL_PORT = "3306"
                MYSQL_USER = "onlyfile_user"
                MYSQL_PASS = "0nlyF!le`$ecure#123"
                MYSQL_DB = "onlyfile"
                ALLOW_INSERT_OPERATION = "true"
                ALLOW_UPDATE_OPERATION = "true"
                ALLOW_DELETE_OPERATION = "false"
                MYSQL_ENABLE_LOGGING = "true"
                PATH = "D:\Program Files\nodejs;C:\Users\<USER>\AppData\Roaming\npm;C:\Windows\System32;C:\Windows"
                NODE_PATH = "C:\Users\<USER>\AppData\Roaming\npm\node_modules"
            }
        }
    }
}

$backupConfig | ConvertTo-Json -Depth 10 | Out-File -FilePath ".cursor\mcp-backup.json" -Encoding UTF8
Write-Host "   备用配置已保存到 .cursor\mcp-backup.json" -ForegroundColor Green

Write-Host ""
Write-Host "5. 建议的解决步骤..." -ForegroundColor Yellow
Write-Host ""
Write-Host "   方案1: 使用当前配置" -ForegroundColor Cyan
Write-Host "   1. 完全关闭Cursor" -ForegroundColor White
Write-Host "   2. 重新启动Cursor" -ForegroundColor White
Write-Host "   3. 在聊天中测试 @mcp_server_mysql" -ForegroundColor White
Write-Host ""
Write-Host "   方案2: 如果方案1不工作，使用备用配置" -ForegroundColor Cyan
Write-Host "   1. 复制 .cursor\mcp-backup.json 的内容" -ForegroundColor White
Write-Host "   2. 替换 .cursor\mcp.json 的内容" -ForegroundColor White
Write-Host "   3. 重启Cursor" -ForegroundColor White
Write-Host ""
Write-Host "   方案3: 使用Smithery安装（推荐）" -ForegroundColor Cyan
Write-Host "   1. 访问 https://smithery.ai/server/@benborla29/mcp-server-mysql" -ForegroundColor White
Write-Host "   2. 按照Cursor的安装说明操作" -ForegroundColor White
Write-Host ""

Write-Host "测试完成！" -ForegroundColor Green
