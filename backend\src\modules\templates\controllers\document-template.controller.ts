import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Param, 
  Body, 
  Query, 
  UseInterceptors, 
  UploadedFile,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiParam, ApiBody, ApiResponse, ApiConsumes } from '@nestjs/swagger';
import { DocumentTemplateService } from '../services/document-template.service';
import { diskStorage } from 'multer';
import { extname } from 'path';
import { Express } from 'express';

/**
 * 文档模板控制器
 * 提供FileNet文档模板的管理API
 * 区别于OnlyOffice配置模板，此控制器管理实际的文档文件模板
 */
@ApiTags('文档模板管理')
@Controller('document-templates')
export class DocumentTemplateController {
  constructor(private documentTemplateService: DocumentTemplateService) {}

  /**
   * 获取文档模板列表
   */
  @Get()
  @ApiOperation({
    summary: '获取文档模板列表',
    description: '获取系统中所有的文档模板，支持分页和筛选'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取文档模板列表',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          properties: {
            templates: { type: 'array' },
            total: { type: 'number', example: 10 },
            limit: { type: 'number', example: 10 },
            offset: { type: 'number', example: 0 }
          }
        },
        message: { type: 'string', example: '获取文档模板列表成功' }
      }
    }
  })
  async getDocumentTemplates(
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('categoryId') categoryId?: string,
    @Query('status') status?: string,
    @Query('sortBy') sortBy?: string,
    @Query('order') order?: string
  ) {
    try {
      const options = {
        limit: limit ? parseInt(limit.toString()) : 10,
        offset: offset ? parseInt(offset.toString()) : 0,
        categoryId,
        status,
        sortBy,
        order
      };

      const result = await this.documentTemplateService.getDocumentTemplates(options);
      
      return {
        success: true,
        data: result.data,
        message: '获取文档模板列表成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException({
        success: false,
        message: '获取文档模板列表失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 根据ID获取文档模板详情
   */
  @Get(':id')
  @ApiOperation({
    summary: '获取文档模板详情',
    description: '根据模板ID获取详细信息'
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiResponse({
    status: 200,
    description: '成功获取文档模板详情'
  })
  @ApiResponse({
    status: 404,
    description: '文档模板不存在'
  })
  async getDocumentTemplateById(@Param('id') templateId: string) {
    try {
      const result = await this.documentTemplateService.getDocumentTemplateById(templateId);
      
      return {
        success: true,
        data: result.data,
        message: '获取文档模板详情成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException({
        success: false,
        message: '获取文档模板详情失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建文档模板
   */
  @Post('create')
  @UseInterceptors(FileInterceptor('file', {
    storage: diskStorage({
      destination: './uploads',
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + extname(file.originalname));
      }
    }),
    limits: {
      fileSize: 50 * 1024 * 1024 // 50MB
    }
  }))
  @ApiOperation({
    summary: '创建文档模板',
    description: '上传文件并创建新的文档模板'
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '文档模板文件和元数据',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: '模板文件'
        },
        name: {
          type: 'string',
          description: '模板名称'
        },
        categoryId: {
          type: 'string',
          description: '分类ID'
        },
        description: {
          type: 'string',
          description: '模板描述'
        },
        createdBy: {
          type: 'string',
          description: '创建者'
        }
      },
      required: ['file', 'name']
    }
  })
  @ApiResponse({
    status: 201,
    description: '文档模板创建成功'
  })
  @ApiResponse({
    status: 400,
    description: '请求参数错误'
  })
  async createDocumentTemplate(
    @Body() templateData: Record<string, unknown>,
    @UploadedFile() file: Express.Multer.File
  ) {
    try {
      // 类型验证和转换
      const createData = {
        name: String(templateData.name || '').trim(),
        categoryId: templateData.categoryId ? String(templateData.categoryId) : undefined,
        description: templateData.description ? String(templateData.description) : undefined,
        createdBy: templateData.createdBy ? String(templateData.createdBy) : 'system'
      };

      // 验证必需字段
      if (!createData.name) {
        throw new HttpException(
          { success: false, message: '模板名称不能为空', timestamp: new Date().toISOString() },
          HttpStatus.BAD_REQUEST
        );
      }

      // 将Express.Multer.File转换为服务期望的类型
      const templateFile = {
        originalname: file.originalname,
        size: file.size,
        filename: file.filename,
        path: file.path,
        mimetype: file.mimetype,
        destination: file.destination
      };

      const result = await this.documentTemplateService.createDocumentTemplate(createData, templateFile);
      
      return {
        success: true,
        data: result.data,
        message: '文档模板创建成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException({
        success: false,
        message: '文档模板创建失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 更新文档模板
   */
  @Put(':id')
  @ApiOperation({
    summary: '更新文档模板',
    description: '更新指定ID的文档模板信息'
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiBody({
    description: '更新的模板数据',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: '模板名称' },
        categoryId: { type: 'string', description: '分类ID' },
        description: { type: 'string', description: '模板描述' },
        status: { type: 'string', enum: ['enabled', 'disabled'], description: '模板状态' },
        updatedBy: { type: 'string', description: '更新者' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: '文档模板更新成功'
  })
  @ApiResponse({
    status: 404,
    description: '文档模板不存在'
  })
  async updateDocumentTemplate(
    @Param('id') templateId: string,
    @Body() updateData: Record<string, unknown>
  ) {
    try {
      const result = await this.documentTemplateService.updateDocumentTemplate(templateId, updateData);
      
      return {
        success: true,
        data: result.data,
        message: '文档模板更新成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException({
        success: false,
        message: '文档模板更新失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 删除文档模板
   */
  @Delete(':id')
  @ApiOperation({
    summary: '删除文档模板',
    description: '软删除指定ID的文档模板'
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiResponse({
    status: 200,
    description: '文档模板删除成功'
  })
  @ApiResponse({
    status: 404,
    description: '文档模板不存在'
  })
  async deleteDocumentTemplate(@Param('id') templateId: string) {
    try {
      const result = await this.documentTemplateService.deleteDocumentTemplate(templateId);
      
      return {
        success: true,
        data: result,
        message: '文档模板删除成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException({
        success: false,
        message: '文档模板删除失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 基于模板创建新文档
   */
  @Post(':id/create-document')
  @ApiOperation({
    summary: '基于模板创建新文档',
    description: '使用指定模板创建新的文档'
  })
  @ApiParam({ name: 'id', description: '模板ID' })
  @ApiBody({
    description: '新文档信息',
    schema: {
      type: 'object',
      properties: {
        newDocumentName: { type: 'string', description: '新文档名称' },
        userId: { type: 'string', description: '用户ID' }
      }
    }
  })
  @ApiResponse({
    status: 201,
    description: '基于模板创建文档成功'
  })
  async createDocumentFromTemplate(
    @Param('id') templateId: string,
    @Body() createData: { newDocumentName: string; userId?: string }
  ) {
    try {
      const { newDocumentName, userId } = createData;
      const result = await this.documentTemplateService.createDocumentFromTemplate(
        templateId,
        newDocumentName,
        userId
      );
      
      return {
        success: true,
        data: result.data,
        message: result.message,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException({
        success: false,
        message: '基于模板创建文档失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取模板分类
   */
  @Get('categories/list')
  @ApiOperation({
    summary: '获取模板分类列表',
    description: '获取所有可用的模板分类'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取模板分类列表'
  })
  async getTemplateCategories() {
    try {
      const result = await this.documentTemplateService.getTemplateCategories();
      
      return {
        success: true,
        data: result.data,
        message: '获取模板分类列表成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException({
        success: false,
        message: '获取模板分类列表失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 创建模板分类
   */
  @Post('categories/create')
  @ApiOperation({
    summary: '创建模板分类',
    description: '创建新的模板分类'
  })
  @ApiBody({
    description: '分类数据',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', description: '分类名称' },
        parentId: { type: 'string', description: '父分类ID' },
        description: { type: 'string', description: '分类描述' },
        sortOrder: { type: 'number', description: '排序顺序' }
      },
      required: ['name']
    }
  })
  @ApiResponse({
    status: 201,
    description: '模板分类创建成功'
  })
  async createTemplateCategory(@Body() categoryData: Record<string, unknown>) {
    try {
      // 类型验证和转换
      const createData = {
        name: String(categoryData.name || '').trim(),
        parentId: categoryData.parentId ? String(categoryData.parentId) : undefined,
        description: categoryData.description ? String(categoryData.description) : undefined,
        sortOrder: categoryData.sortOrder ? Number(categoryData.sortOrder) : undefined
      };

      // 验证必需字段
      if (!createData.name) {
        throw new HttpException(
          { success: false, message: '分类名称不能为空', timestamp: new Date().toISOString() },
          HttpStatus.BAD_REQUEST
        );
      }

      const result = await this.documentTemplateService.createTemplateCategory(createData);

      return {
        success: true,
        data: result.data,
        message: '模板分类创建成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException({
        success: false,
        message: '模板分类创建失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取所有模板概览
   */
  @Get('overview/all')
  @ApiOperation({
    summary: '获取所有模板概览',
    description: '获取包括文档模板和配置模板在内的所有模板概览'
  })
  @ApiResponse({
    status: 200,
    description: '成功获取模板概览'
  })
  async getAllTemplatesOverview() {
    try {
      const result = await this.documentTemplateService.getAllTemplatesOverview();
      
      return {
        success: true,
        data: result.data,
        message: '获取模板概览成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException({
        success: false,
        message: '获取模板概览失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 搜索模板
   */
  @Get('search/all')
  @ApiOperation({
    summary: '搜索模板',
    description: '在文档模板和配置模板中搜索'
  })
  @ApiResponse({
    status: 200,
    description: '搜索完成'
  })
  async searchTemplates(
    @Query('keyword') keyword?: string,
    @Query('type') type?: string,
    @Query('limit') limit?: number
  ) {
    try {
      const searchOptions = {
        keyword,
        type,
        limit: limit ? parseInt(limit.toString()) : 20
      };

      const result = await this.documentTemplateService.searchTemplates(searchOptions);
      
      return {
        success: true,
        data: result.data,
        message: '模板搜索完成',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new HttpException({
        success: false,
        message: '模板搜索失败',
        error: error.message,
        timestamp: new Date().toISOString()
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
} 