<template>
  <div class="document-actions">
    <a-space :size="4">
      <!-- 内嵌编辑按钮 -->
      <a-button type="primary" size="small" @click="handleEmbeddedEdit" :loading="embeddedLoading">
        <template #icon>
          <edit-outlined />
        </template>
        内嵌
      </a-button>

      <!-- 新窗口编辑按钮 -->
      <a-button size="small" @click="handlePopupEdit" :loading="popupLoading">
        <template #icon>
          <link-outlined />
        </template>
        新窗
      </a-button>

      <!-- 配置模板选择下拉 -->
      <a-select
        v-model:value="selectedTemplateId"
        size="small"
        style="width: 90px"
        :loading="templateLoading"
        placeholder="配置"
        @change="handleTemplateChange"
      >
        <a-select-option
          v-for="template in configTemplates"
          :key="template.id"
          :value="template.id"
        >
          <span :title="template.description">{{ template.name }}</span>
        </a-select-option>
      </a-select>
    </a-space>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { EditOutlined, LinkOutlined } from '@ant-design/icons-vue'
import { openEmbeddedEditor, openPopupEditor } from '@/utils/editor-utils'
import type { EditorConfigQuery } from '@/utils/editor-utils'

/**
 * 文档操作组件
 *
 * @description 提供文档编辑操作，支持内嵌和弹窗两种模式，以及配置模板选择
 * <AUTHOR> Integration Team
 * @since 2024-12-19
 */

interface Props {
  /** 文档ID */
  documentId: string
}

interface ConfigTemplate {
  id: string
  name: string
  description: string
  isDefault: boolean
  isActive: boolean
  updatedAt: string
}

const props = defineProps<Props>()

// 加载状态
const embeddedLoading = ref(false)
const popupLoading = ref(false)
const templateLoading = ref(false)

// 配置模板选择
const configTemplates = ref<ConfigTemplate[]>([])
const selectedTemplateId = ref<string>('')

/**
 * 加载配置模板列表
 */
const loadConfigTemplates = async (): Promise<void> => {
  templateLoading.value = true
  try {
    const response = await fetch('http://*************:3000/api/config-templates')
    const result = await response.json()

    if (result.success && result.data) {
      configTemplates.value = result.data.filter((template: ConfigTemplate) => template.isActive)

      // 设置默认选中的模板
      const defaultTemplate = configTemplates.value.find(template => template.isDefault)
      if (defaultTemplate) {
        selectedTemplateId.value = defaultTemplate.id
      } else if (configTemplates.value.length > 0) {
        selectedTemplateId.value = configTemplates.value[0].id
      }

      console.log('✅ [DocumentActions] 配置模板加载成功:', configTemplates.value)
    } else {
      console.warn('⚠️ [DocumentActions] 配置模板加载失败:', result)
      message.warning('加载配置模板失败，将使用默认配置')
    }
  } catch (error) {
    console.error('❌ [DocumentActions] 配置模板加载错误:', error)
    message.error('加载配置模板失败')
  } finally {
    templateLoading.value = false
  }
}

/**
 * 获取当前选中的配置模板ID
 * @returns 配置模板ID，如果没有选中则返回默认模板ID
 */
const getCurrentTemplateId = (): string | undefined => {
  if (selectedTemplateId.value) {
    return selectedTemplateId.value
  }

  // 如果没有选中模板，尝试使用默认模板
  const defaultTemplate = configTemplates.value.find(template => template.isDefault)
  if (defaultTemplate) {
    selectedTemplateId.value = defaultTemplate.id
    return defaultTemplate.id
  }

  // 如果没有默认模板，使用第一个可用模板
  if (configTemplates.value.length > 0) {
    selectedTemplateId.value = configTemplates.value[0].id
    return configTemplates.value[0].id
  }

  return undefined
}

/**
 * 处理配置模板变更
 */
const handleTemplateChange = (templateId: string): void => {
  selectedTemplateId.value = templateId
  const template = configTemplates.value.find(t => t.id === templateId)
  if (template) {
    message.success(`已选择配置模板: ${template.name}`)
  }
}

/**
 * 处理内嵌编辑
 */
const handleEmbeddedEdit = async (): Promise<void> => {
  embeddedLoading.value = true

  try {
    // 获取当前配置模板
    const templateId = getCurrentTemplateId()
    const configQuery: EditorConfigQuery = templateId ? { template: templateId } : {}

    console.log('🚀 [DocumentActions] handleEmbeddedEdit 开始内嵌编辑')
    console.log('📋 [DocumentActions] 文档ID:', props.documentId)
    console.log('🔧 [DocumentActions] 配置模板ID:', templateId)

    await openEmbeddedEditor(props.documentId, configQuery)
    message.success('正在打开编辑器...')
  } catch (error) {
    console.error('❌ [DocumentActions] 内嵌编辑失败:', error)
    message.error('内嵌编辑器打开失败')
  } finally {
    embeddedLoading.value = false
  }
}

/**
 * 处理弹窗编辑
 */
const handlePopupEdit = async (): Promise<void> => {
  popupLoading.value = true

  try {
    // 获取当前配置模板
    const templateId = getCurrentTemplateId()
    const configQuery: EditorConfigQuery = templateId ? { template: templateId } : {}

    console.log('🚀 [DocumentActions] handlePopupEdit 开始弹窗编辑')
    console.log('📋 [DocumentActions] 文档ID:', props.documentId)
    console.log('🔧 [DocumentActions] 配置模板ID:', templateId)

    const editorWindow = await openPopupEditor(props.documentId, configQuery)

    if (editorWindow) {
      message.success('编辑器已在新窗口中打开')

      // 监听窗口关闭事件
      const checkClosed = setInterval(() => {
        if (editorWindow.closed) {
          clearInterval(checkClosed)
          message.info('编辑器窗口已关闭')
        }
      }, 1000)
    }
  } catch (error) {
    console.error('❌ [DocumentActions] 弹窗编辑失败:', error)
    message.error(`打开编辑器失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    popupLoading.value = false
  }
}

// 组件挂载时加载配置模板
onMounted(() => {
  loadConfigTemplates()
})
</script>

<style scoped>
.document-actions {
  display: flex;
  align-items: center;
}

/* 优化下拉选择器样式 */
:deep(.ant-select-selector) {
  border-radius: 4px;
  font-size: 12px;
}

:deep(.ant-select-selection-item) {
  font-size: 12px;
}

/* 优化按钮样式 */
:deep(.ant-btn-sm) {
  padding: 0 6px;
  font-size: 12px;
  height: 24px;
  line-height: 22px;
}

:deep(.ant-btn-sm .anticon) {
  font-size: 12px;
}
</style>
