"use strict";
exports.id = 0;
exports.ids = null;
exports.modules = {

/***/ 50:
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.FilenetService = void 0;
const common_1 = __webpack_require__(5);
const axios_1 = __webpack_require__(19);
const config_1 = __webpack_require__(8);
const database_service_1 = __webpack_require__(15);
const rxjs_1 = __webpack_require__(51);
const FormData = __webpack_require__(52);
const fs = __webpack_require__(13);
const crypto = __webpack_require__(53);
const uuid_1 = __webpack_require__(28);
const stream_1 = __webpack_require__(54);
let FilenetService = class FilenetService {
    constructor(httpService, configService, databaseService) {
        this.httpService = httpService;
        this.configService = configService;
        this.databaseService = databaseService;
        this.filenetHost = this.configService.get('FILENET_HOST') || '*************';
        this.filenetPort = this.configService.get('FILENET_PORT') || '8090';
        this.defaultFolder = this.configService.get('FILENET_DEFAULT_FOLDER') || '{2FFE1C9C-3EF4-4467-808D-99F85F42531F}';
        this.defaultDocClass = this.configService.get('FILENET_DEFAULT_DOC_CLASS') || 'SimpleDocument';
        this.defaultSourceType = this.configService.get('FILENET_DEFAULT_SOURCE_TYPE') || 'MaxOffice';
        this.defaultBizTag = this.configService.get('FILENET_DEFAULT_BIZ_TAG') || 'office_file';
        console.log('[FilenetService] 初始化配置:', {
            host: this.filenetHost,
            port: this.filenetPort,
            defaultFolder: this.defaultFolder,
            defaultDocClass: this.defaultDocClass,
            defaultSourceType: this.defaultSourceType,
            defaultBizTag: this.defaultBizTag
        });
    }
    async calculateFileHash(filePath) {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash('sha256');
            const stream = fs.createReadStream(filePath);
            stream.on('data', data => hash.update(data));
            stream.on('end', () => resolve(hash.digest('hex')));
            stream.on('error', error => reject(error));
        });
    }
    calculateFileHashFromBuffer(buffer) {
        const hash = crypto.createHash('sha256');
        hash.update(buffer);
        return hash.digest('hex');
    }
    getFileExtension(filename) {
        return filename.split('.').pop()?.toLowerCase() || '';
    }
    getMimeType(extension) {
        const mimeTypes = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'txt': 'text/plain',
            'xml': 'application/xml'
        };
        return mimeTypes[extension] || 'application/octet-stream';
    }
    sanitizeFileName(filename) {
        if (!filename)
            return 'untitled';
        try {
            let cleanName = filename;
            if (/%[0-9A-F]{2}/.test(filename)) {
                try {
                    cleanName = decodeURIComponent(filename);
                }
                catch (e) {
                    console.warn('解码文件名失败，使用原始文件名:', e);
                }
            }
            cleanName = cleanName.replace(/[<>:"/\\|?*]/g, '_');
            if (cleanName.length > 200) {
                const ext = this.getFileExtension(cleanName);
                const nameWithoutExt = cleanName.substring(0, cleanName.lastIndexOf('.'));
                cleanName = nameWithoutExt.substring(0, 200 - ext.length - 1) + '.' + ext;
            }
            return cleanName;
        }
        catch (error) {
            console.error('清理文件名时出错:', error);
            return 'untitled_' + Date.now();
        }
    }
    async uploadFileToFileNetOnly(fileBuffer, originalFileName, mimeType, _userId = 'anonymous') {
        try {
            console.log(`[FilenetService] 仅上传到FileNet: ${originalFileName}`);
            if (!this.filenetHost) {
                throw new Error('FileNet 配置缺失，无法上传文件');
            }
            const sanitizedFileName = this.sanitizeFileName(originalFileName);
            const uploadUrl = `http://${this.filenetHost}:${this.filenetPort}/common/uploadFiles`;
            const formData = new FormData();
            formData.append('folder', this.defaultFolder);
            formData.append('docClass', this.defaultDocClass);
            formData.append('docName', sanitizedFileName);
            formData.append('source_type', this.defaultSourceType);
            formData.append('biz_tag', this.defaultBizTag);
            const bufferStream = new stream_1.Readable();
            bufferStream.push(fileBuffer);
            bufferStream.push(null);
            formData.append('file', bufferStream, sanitizedFileName);
            console.log(`[FilenetService] 上传参数:`, {
                url: uploadUrl,
                folder: this.defaultFolder,
                docClass: this.defaultDocClass,
                docName: sanitizedFileName,
                fileSize: fileBuffer.length
            });
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(uploadUrl, formData, {
                headers: {
                    ...formData.getHeaders(),
                    'Connection': 'keep-alive'
                },
                maxBodyLength: Infinity,
                maxContentLength: Infinity,
                timeout: 60000,
            }));
            console.log('[FilenetService] FileNet 上传响应状态:', response.status);
            console.log('[FilenetService] FileNet 上传响应数据:', response.data);
            let fnDocId;
            if (response.status === 200) {
                if (typeof response.data === 'string' && response.data.startsWith('{') && response.data.endsWith('}')) {
                    fnDocId = response.data;
                }
                else if (response.data && response.data.code === '0' && response.data.data && typeof response.data.data === 'string' && response.data.data.startsWith('{') && response.data.data.endsWith('}')) {
                    fnDocId = response.data.data;
                }
                else if (response.data && response.data.data && typeof response.data.data === 'string' && response.data.data.startsWith('{') && response.data.data.endsWith('}')) {
                    fnDocId = response.data.data;
                }
                else {
                    let failureMessage = 'FileNet 上传失败或响应格式不正确';
                    if (response.data && typeof response.data === 'object' && response.data.message) {
                        failureMessage = `FileNet 操作消息: ${response.data.message}`;
                    }
                    else if (typeof response.data === 'string' && response.data.length < 200) {
                        failureMessage = `FileNet 响应: ${response.data}`;
                    }
                    console.error('[FilenetService] ' + failureMessage, response.data);
                    throw new Error(failureMessage);
                }
                console.log('[FilenetService] 文件已上传到FileNet, fn_doc_id:', fnDocId);
                return fnDocId;
            }
            else {
                throw new Error(`FileNet上传失败，状态码: ${response.status}`);
            }
        }
        catch (error) {
            console.error('[FilenetService] 仅上传到FileNet失败:', error);
            throw error;
        }
    }
    async uploadDocument(fileData, originalName, userId = 'anonymous') {
        try {
            console.log(`[FilenetService] 开始上传文档到FileNet: ${originalName}`);
            console.log(`[FilenetService] 文件数据对象:`, {
                originalname: fileData.originalname,
                filename: fileData.filename,
                path: fileData.path,
                destination: fileData.destination,
                size: fileData.size,
                buffer: fileData.buffer ? 'Buffer存在' : '无Buffer',
                mimetype: fileData.mimetype
            });
            if (!this.filenetHost) {
                throw new Error('FileNet 配置缺失，无法上传文件');
            }
            const fileSize = fileData.size;
            const originalFileName = this.sanitizeFileName(fileData.originalname || originalName);
            const mimeType = fileData.mimetype || this.getMimeType(this.getFileExtension(originalFileName));
            console.log(`[FilenetService] 解析的文件信息:`, {
                fileSize,
                originalFileName,
                mimeType,
                hasBuffer: !!fileData.buffer,
                hasPath: !!fileData.path
            });
            if (typeof fileSize === 'undefined' || !originalFileName) {
                console.error(`[FilenetService] 文件数据不完整:`, {
                    fileSize: fileSize,
                    originalFileName: originalFileName,
                    fullFileData: fileData
                });
                throw new Error('无效的文件数据，缺少大小或原始文件名');
            }
            let fileHash;
            let fileBuffer;
            if (fileData.buffer) {
                console.log(`[FilenetService] 使用内存缓冲区处理文件`);
                fileBuffer = fileData.buffer;
                fileHash = this.calculateFileHashFromBuffer(fileBuffer);
            }
            else if (fileData.path && fs.existsSync(fileData.path)) {
                console.log(`[FilenetService] 使用磁盘文件: ${fileData.path}`);
                fileBuffer = fs.readFileSync(fileData.path);
                fileHash = await this.calculateFileHash(fileData.path);
            }
            else {
                const filePath = fileData.path || (fileData.destination && fileData.filename ? fileData.destination + '/' + fileData.filename : null);
                if (filePath && fs.existsSync(filePath)) {
                    console.log(`[FilenetService] 使用组合路径: ${filePath}`);
                    fileBuffer = fs.readFileSync(filePath);
                    fileHash = await this.calculateFileHash(filePath);
                }
                else {
                    throw new Error('无法获取文件数据：既没有buffer也没有有效的文件路径');
                }
            }
            const extension = this.getFileExtension(originalFileName);
            console.log(`[FilenetService] 文件信息:`, {
                fileHash,
                extension,
                mimeType,
                bufferSize: fileBuffer.length
            });
            const existingDoc = await this.databaseService.queryOne('SELECT fn_doc_id FROM filenet_documents WHERE file_hash = ? AND is_deleted = FALSE ORDER BY created_at DESC LIMIT 1', [fileHash]);
            let fnDocId;
            let contentWasReused = false;
            if (existingDoc && existingDoc.fn_doc_id) {
                console.log(`[FilenetService] 内容哈希已存在，复用FileNet文档ID: ${existingDoc.fn_doc_id}`);
                fnDocId = existingDoc.fn_doc_id;
                contentWasReused = true;
            }
            else {
                const uploadUrl = `http://${this.filenetHost}:${this.filenetPort}/common/uploadFiles`;
                const formData = new FormData();
                formData.append('folder', this.defaultFolder);
                formData.append('docClass', this.defaultDocClass);
                formData.append('docName', originalFileName);
                formData.append('source_type', this.defaultSourceType);
                formData.append('biz_tag', this.defaultBizTag);
                const bufferStream = new stream_1.Readable();
                bufferStream.push(fileBuffer);
                bufferStream.push(null);
                formData.append('file', bufferStream, originalFileName);
                console.log(`[FilenetService] 上传参数:`, {
                    url: uploadUrl,
                    folder: this.defaultFolder,
                    docClass: this.defaultDocClass,
                    docName: originalFileName,
                    source_type: this.defaultSourceType,
                    biz_tag: this.defaultBizTag,
                    fileSize: fileBuffer.length
                });
                const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(uploadUrl, formData, {
                    headers: {
                        ...formData.getHeaders(),
                        'Connection': 'keep-alive'
                    },
                    maxBodyLength: Infinity,
                    maxContentLength: Infinity,
                    timeout: 60000,
                }));
                console.log('[FilenetService] FileNet 上传响应状态:', response.status);
                console.log('[FilenetService] FileNet 上传响应数据:', response.data);
                if (response.status === 200) {
                    if (typeof response.data === 'string' && response.data.startsWith('{') && response.data.endsWith('}')) {
                        fnDocId = response.data;
                    }
                    else if (response.data && response.data.code === '0' && response.data.data && typeof response.data.data === 'string' && response.data.data.startsWith('{') && response.data.data.endsWith('}')) {
                        fnDocId = response.data.data;
                    }
                    else if (response.data && response.data.data && typeof response.data.data === 'string' && response.data.data.startsWith('{') && response.data.data.endsWith('}')) {
                        fnDocId = response.data.data;
                    }
                    else {
                        let failureMessage = 'FileNet 上传失败或响应格式不正确';
                        if (response.data && typeof response.data === 'object' && response.data.message) {
                            failureMessage = `FileNet 操作消息: ${response.data.message}`;
                        }
                        else if (typeof response.data === 'string' && response.data.length < 200) {
                            failureMessage = `FileNet 响应: ${response.data}`;
                        }
                        console.error('[FilenetService] ' + failureMessage, response.data);
                        throw new Error(failureMessage);
                    }
                    console.log('[FilenetService] 新内容已上传到FileNet, 得到的 fn_doc_id:', fnDocId);
                }
                else {
                    throw new Error(`FileNet上传失败，状态码: ${response.status}`);
                }
            }
            let documentId;
            let existingRecord = null;
            if (contentWasReused) {
                existingRecord = await this.databaseService.queryOne('SELECT id FROM filenet_documents WHERE fn_doc_id = ? AND file_hash = ? AND is_deleted = FALSE LIMIT 1', [fnDocId, fileHash]);
                if (existingRecord) {
                    console.log(`[FilenetService] 内容复用，使用现有数据库记录: ${existingRecord.id}`);
                    documentId = existingRecord.id;
                }
            }
            if (!existingRecord) {
                documentId = (0, uuid_1.v4)();
                try {
                    await this.databaseService.query('INSERT INTO filenet_documents (id, fn_doc_id, original_name, file_size, mime_type, extension, version, file_hash, created_by, last_modified_by, uploaded_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)', [documentId, fnDocId, originalFileName, fileSize, mimeType, extension, 1, fileHash, userId, userId]);
                    console.log(`[FilenetService] 新文档记录已创建: ${documentId}`);
                }
                catch (dbError) {
                    if (dbError.code === 'ER_DUP_ENTRY' && dbError.message.includes('unique_fn_doc_id')) {
                        console.error(`[FilenetService] fn_doc_id重复错误: ${fnDocId}`);
                        const conflictingRecord = await this.databaseService.queryOne('SELECT id FROM filenet_documents WHERE fn_doc_id = ? AND is_deleted = FALSE LIMIT 1', [fnDocId]);
                        if (conflictingRecord) {
                            console.warn(`[FilenetService] 使用冲突记录ID: ${conflictingRecord.id}`);
                            documentId = conflictingRecord.id;
                        }
                        else {
                            throw dbError;
                        }
                    }
                    else {
                        throw dbError;
                    }
                }
            }
            const versionsTableExists = await this.databaseService.queryOne("SHOW TABLES LIKE 'filenet_document_versions'");
            if (versionsTableExists) {
                const existingVersion = await this.databaseService.queryOne('SELECT id FROM filenet_document_versions WHERE doc_id = ? AND fn_doc_id = ? AND file_hash = ?', [documentId, fnDocId, fileHash]);
                if (!existingVersion) {
                    const versionId = (0, uuid_1.v4)();
                    const currentDocInfo = await this.databaseService.queryOne('SELECT version FROM filenet_documents WHERE id = ?', [documentId]);
                    const newVersionNumber = currentDocInfo ? (currentDocInfo.version || 1) : 1;
                    await this.databaseService.query('INSERT INTO filenet_document_versions (id, doc_id, fn_doc_id, version, file_hash, modified_by, file_size, comment) VALUES (?, ?, ?, ?, ?, ?, ?, ?)', [versionId, documentId, fnDocId, newVersionNumber, fileHash, userId, fileSize, 'New version from upload']);
                    console.log(`[FilenetService] 版本记录已创建: doc_id ${documentId}, version ${newVersionNumber}`);
                }
            }
            return {
                success: true,
                message: '文件上传成功处理',
                docId: fnDocId,
                dbId: documentId,
                originalName: originalFileName,
                fileHash: fileHash,
                contentWasReused: contentWasReused
            };
        }
        catch (error) {
            console.error('[FilenetService] 上传文档到FileNet失败:', error.message);
            throw new Error(`上传文档到FileNet失败: ${error.message}`);
        }
    }
    async downloadDocument(fnDocId) {
        try {
            console.log(`[FilenetService] 从FileNet下载文档: ${fnDocId}`);
            if (!this.filenetHost) {
                throw new Error('FileNet configuration is missing or incomplete.');
            }
            const cleanDocId = fnDocId.replace(/[{}]/g, "");
            const downloadUrl = `http://${this.filenetHost}:${this.filenetPort}/common/download?docId=${encodeURIComponent(cleanDocId)}`;
            console.log(`[FilenetService] 准备从FileNet下载: ${downloadUrl}`);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(downloadUrl, {
                responseType: 'stream',
                timeout: 60000,
            }));
            console.log('[FilenetService] FileNet 下载响应状态:', response.status);
            let fileName = `filenet_document_${cleanDocId}`;
            const disposition = response.headers['content-disposition'];
            if (disposition) {
                const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                const matches = filenameRegex.exec(disposition);
                if (matches != null && matches[1]) {
                    fileName = matches[1].replace(/['"]/g, '');
                    try {
                        fileName = decodeURIComponent(fileName);
                    }
                    catch (error) {
                        console.warn('[FilenetService] 解码文件名失败, 使用原始文件名:', error instanceof Error ? error.message : String(error));
                    }
                }
            }
            console.log('[FilenetService] 推断的文件名:', fileName);
            return {
                success: true,
                fileName: fileName,
                stream: response.data
            };
        }
        catch (error) {
            console.error('[FilenetService] 从FileNet下载文档失败:', error.message);
            if (error.response) {
                console.error('[FilenetService] FileNet错误响应状态:', error.response.status);
            }
            throw new common_1.HttpException(`从FileNet下载文档失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDocumentInfo(fnDocId) {
        try {
            const document = await this.databaseService.queryOne('SELECT * FROM filenet_documents WHERE fn_doc_id = ? AND is_deleted = FALSE', [fnDocId]);
            if (!document) {
                throw new Error('文档不存在');
            }
            return {
                success: true,
                data: document
            };
        }
        catch (error) {
            console.error('[FilenetService] 获取文档信息失败:', error.message);
            throw new common_1.HttpException(`获取文档信息失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteDocument(fnDocId) {
        try {
            console.log(`[FilenetService] 删除FileNet文档: ${fnDocId}`);
            await this.databaseService.query('UPDATE filenet_documents SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP WHERE fn_doc_id = ?', [fnDocId]);
            return {
                success: true,
                message: '文档删除成功'
            };
        }
        catch (error) {
            console.error('[FilenetService] 删除FileNet文档失败:', error.message);
            throw new common_1.HttpException(`删除FileNet文档失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async updateDocumentVersion(fnDocId, _fileData, _versionComment) {
        try {
            console.log(`[FilenetService] 更新FileNet文档版本: ${fnDocId}`);
            return {
                success: true,
                message: '文档版本更新成功',
                fnDocId: fnDocId
            };
        }
        catch (error) {
            console.error('[FilenetService] 更新FileNet文档版本失败:', error.message);
            throw new common_1.HttpException(`更新FileNet文档版本失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getDocumentVersions(fnDocId) {
        try {
            console.log(`[FilenetService] 获取文档版本列表: ${fnDocId}`);
            const versions = await this.databaseService.query('SELECT * FROM filenet_document_versions WHERE fn_doc_id = ? ORDER BY version DESC', [fnDocId]);
            return {
                success: true,
                data: versions
            };
        }
        catch (error) {
            console.error('[FilenetService] 获取文档版本列表失败:', error.message);
            throw new common_1.HttpException(`获取文档版本列表失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async searchDocuments(searchOptions) {
        try {
            console.log('[FilenetService] 搜索FileNet文档:', searchOptions);
            let query = 'SELECT * FROM filenet_documents WHERE is_deleted = FALSE';
            const params = [];
            if (searchOptions.filename) {
                query += ' AND original_name LIKE ?';
                params.push(`%${searchOptions.filename}%`);
            }
            if (searchOptions.extension) {
                query += ' AND extension = ?';
                params.push(searchOptions.extension);
            }
            if (searchOptions.dateFrom) {
                query += ' AND uploaded_at >= ?';
                params.push(searchOptions.dateFrom);
            }
            if (searchOptions.dateTo) {
                query += ' AND uploaded_at <= ?';
                params.push(searchOptions.dateTo);
            }
            query += ' ORDER BY uploaded_at DESC';
            if (searchOptions.limit) {
                query += ' LIMIT ?';
                params.push(searchOptions.limit.toString());
            }
            const documents = await this.databaseService.query(query, params);
            return {
                success: true,
                data: documents,
                total: documents.length
            };
        }
        catch (error) {
            console.error('[FilenetService] 搜索文档失败:', error.message);
            throw new common_1.HttpException(`搜索文档失败: ${error.message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async testConnection() {
        try {
            console.log('[FilenetService] 测试FileNet连接...');
            if (!this.filenetHost) {
                throw new Error('FileNet配置缺失');
            }
            const testUrl = `http://${this.filenetHost}:${this.filenetPort}/common/uploadFiles`;
            console.log(`[FilenetService] 测试连接URL: ${testUrl}`);
            try {
                const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(testUrl, {
                    timeout: 10000,
                }));
                console.log(`[FilenetService] 连接测试响应状态: ${response.status}`);
                console.log('[FilenetService] FileNet连接测试成功 (GET方法)');
                return {
                    status: 'up',
                    message: 'FileNet连接正常',
                    url: testUrl,
                    timestamp: new Date().toISOString()
                };
            }
            catch (error) {
                if (error.response && error.response.status === 405) {
                    console.log('[FilenetService] FileNet服务器运行正常 (405 Method Not Allowed是预期的)');
                    return {
                        status: 'up',
                        message: 'FileNet服务器运行正常 (上传端点可用)',
                        url: testUrl,
                        timestamp: new Date().toISOString(),
                        note: 'GET方法不被支持，但服务器正在运行'
                    };
                }
                try {
                    console.log('[FilenetService] 尝试使用OPTIONS方法测试连接...');
                    const optionsResponse = await (0, rxjs_1.firstValueFrom)(this.httpService.request({
                        method: 'OPTIONS',
                        url: testUrl,
                        timeout: 5000,
                    }));
                    console.log(`[FilenetService] FileNet连接测试成功 (OPTIONS方法), 状态: ${optionsResponse.status}`);
                    return {
                        status: 'up',
                        message: 'FileNet连接正常 (OPTIONS响应)',
                        url: testUrl,
                        timestamp: new Date().toISOString()
                    };
                }
                catch {
                    throw error;
                }
            }
        }
        catch (error) {
            console.error('[FilenetService] FileNet连接测试失败:', error.message);
            if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
                return {
                    status: 'down',
                    message: `FileNet服务器无法连接: ${error.message}`,
                    url: `http://${this.filenetHost}:${this.filenetPort}`,
                    timestamp: new Date().toISOString(),
                    error_code: error.code
                };
            }
            return {
                status: 'down',
                message: `FileNet连接失败: ${error.message}`,
                url: `http://${this.filenetHost}:${this.filenetPort}`,
                timestamp: new Date().toISOString()
            };
        }
    }
    generateUUID() {
        return (0, uuid_1.v4)();
    }
};
exports.FilenetService = FilenetService;
exports.FilenetService = FilenetService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof axios_1.HttpService !== "undefined" && axios_1.HttpService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof database_service_1.DatabaseService !== "undefined" && database_service_1.DatabaseService) === "function" ? _c : Object])
], FilenetService);


/***/ })

};
exports.runtime =
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("6f4d070f4467642cc740")
/******/ })();
/******/ 
/******/ }
;